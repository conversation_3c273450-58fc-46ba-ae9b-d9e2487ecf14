[{"ReportColumnId": "Report.GregorianDate", "DBColumnName": "GregorianDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.Week", "DBColumnName": "WeekStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.WeekStartingMonday", "DBColumnName": "WeekStartDateMonday", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.Month", "DBColumnName": "MonthStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.Year", "DBColumnName": "YearNum", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "ReportSmallInt.Year", "DBColumnName": "YearNum", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.HourOfDay", "DBColumnName": "HourOfDay", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.Hourly", "DBColumnName": "HourOfDay", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": ["Report.GregorianDate", "Report.HourOfDay"], "IsBIColumn": false, "IsISColumn": false, "CalculationFunction": {"Default": {"FunctionType": "DateTimeAddHour"}}, "HiddenFromUI": true}, {"ReportColumnId": "Report.DayOfWeek", "DBColumnName": "DayOfWeek", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true, "Category": ["Time"], "IsStackedColumn": true}, {"ReportColumnId": "Report.TimePeriod-<PERSON><PERSON><PERSON>", "DBColumnName": "TimePeriod", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.TimePeriod", "DBColumnName": "TimePeriod", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.TimePeriod-<PERSON><PERSON><PERSON>"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.TimePeriod-HourOfDay", "DBColumnName": "HourOfDay", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "Hour", "HiddenFromUI": true}, {"ReportColumnId": "Report.TimePeriod-Hourly", "DBColumnName": "TimePeriod", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": ["Report.GregorianDate", "Report.HourOfDay"], "IsBIColumn": false, "IsISColumn": false, "CalculationFunction": {"Default": {"FunctionType": "DateTimeAddHour"}}, "HiddenFromUI": true}, {"ReportColumnId": "ReportFormatV2.TimePeriod-Hourly", "DBColumnName": "TimePeriod", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": ["Report.GregorianDate", "Report.HourOfDay"], "IsBIColumn": false, "IsISColumn": false, "CalculationFunction": {"Default": {"FunctionType": "DateTimeAddHour"}}, "HiddenFromUI": true}, {"ReportColumnId": "Report.TimePeriod-Daily", "DBColumnName": "GregorianDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.TimePeriod-DayOfWeek", "DBColumnName": "DayOfWeek", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "Weekday", "HiddenFromUI": true}, {"ReportColumnId": "Report.TimePeriod-Weekly", "DBColumnName": "WeekStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.TimePeriod-WeeklyStartingMonday", "DBColumnName": "WeekStartDateMonday", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.TimePeriod-Monthly", "DBColumnName": "MonthStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.TimePeriod-Yearly", "DBColumnName": "YearNum", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "Year", "HiddenFromUI": true}, {"ReportColumnId": "ReportSmallInt.TimePeriod-Yearly", "DBColumnName": "YearNum", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "Year", "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.<PERSON>", "DBColumnName": "GregorianDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.Week", "DBColumnName": "WeekStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.WeekStartingMonday", "DBColumnName": "WeekStartDateMonday", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Time"], "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.Month", "DBColumnName": "MonthStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.Year", "DBColumnName": "YearNum", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.HourOfDay", "DBColumnName": "HourOfDay", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.<PERSON>ly", "DBColumnName": "HourOfDay", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": ["ReportNull.<PERSON>", "ReportNull.HourOfDay"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true, "CalculationFunction": {"Default": {"FunctionType": "DateTimeAddHour"}}}, {"ReportColumnId": "ReportNull.DayOfWeek", "DBColumnName": "DayOfWeek", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.TimePeriod-HourOfDay", "DBColumnName": "HourOfDay", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true, "DisplayType": "Hour"}, {"ReportColumnId": "ReportNull.TimePeriod-Hourly", "DBColumnName": "TimePeriod", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": ["ReportNull.<PERSON>", "ReportNull.HourOfDay"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true, "CalculationFunction": {"Default": {"FunctionType": "DateTimeAddHour"}}}, {"ReportColumnId": "ReportNull.TimePeriod-Daily", "DBColumnName": "GregorianDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.TimePeriod-DayOfWeek", "DBColumnName": "DayOfWeek", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true, "DisplayType": "Weekday"}, {"ReportColumnId": "ReportNull.TimePeriod-Weekly", "DBColumnName": "WeekStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.TimePeriod-WeeklyStartingMonday", "DBColumnName": "WeekStartDateMonday", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.TimePeriod-Monthly", "DBColumnName": "MonthStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true}, {"ReportColumnId": "ReportNull.TimePeriod-Yearly", "DBColumnName": "YearNum", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true, "DisplayType": "Year"}, {"ReportColumnId": "Report.CustomerId", "DBColumnName": "CustomerId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID"}, {"ReportColumnId": "Report.CustomerName", "DBColumnName": "CustomerName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.AccountId", "DBColumnName": "AccountId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "ReportHidden.AccountId", "DBColumnName": "AccountId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "HiddenFromUI": true}, {"ReportColumnId": "Report.AccountName", "DBColumnName": "Account<PERSON><PERSON>", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "ReportNull.AccountName", "DBColumnName": "Account<PERSON><PERSON>", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.AccountNumber", "DBColumnName": "AccountNumber", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.AccountStatus", "DBColumnName": "AccountStatusName", "DBFilterAlias": "AccountStatusId", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData"}, {"ReportColumnId": "Report.CampaignId", "DBColumnName": "CampaignId", "DBFilterAlias": "CampaignId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.CampaignName", "DBColumnName": "CampaignName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AdDistribution", "DBColumnName": "MediumName", "DBFilterAlias": "MediumId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery"], "IsStackedColumn": true}, {"ReportColumnId": "Report.AdGroupId", "DBColumnName": "OrderId", "DBFilterAlias": "OrderId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.FeedId", "DBColumnName": "FeedId", "DBFilterAlias": "FeedId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "DisplayType": "ID"}, {"ReportColumnId": "Report.FeedItemId", "DBColumnName": "FeedItemId", "DBFilterAlias": "FeedItemId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.AdGroupName", "DBColumnName": "KeywordOrderName", "DBFilterAlias": "AdGroupName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AdGroupStatus", "DBColumnName": "AdGroupStatusName", "DBFilterAlias": "AdGroupStatusId", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery"], "IsStackedColumn": true}, {"ReportColumnId": "AdGroupReport.Status", "DBColumnName": "AdGroupStatusName", "DBFilterAlias": "AdGroupStatusId", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["AdAttributes"], "FilterType": "DomainData"}, {"ReportColumnId": "Report.AdId", "DBColumnName": "AdId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AdRelevance", "DBColumnName": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"], "IsExcludedForISDimension": true}, {"ReportColumnId": "Report2.AdRelevance", "DBColumnName": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.AdRelevanceCampaign", "DBColumnName": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.AdRelevanceAdGroup", "DBColumnName": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.AdRelevanceKeyword", "DBColumnName": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.AdType", "DBColumnName": "AdTypeId", "DBFilterAlias": "AdTypeId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["LevelOfDetail"], "IsStackedColumn": true}, {"ReportColumnId": "Report.BidMatchType", "DBColumnName": "BiddedMatchTypeDesc", "DBFilterAlias": "BiddedMatchTypeId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery", "BudgetBids"], "IsStackedColumn": true}, {"ReportColumnId": "Report.BidStrategyType", "DBColumnName": "BiddingSchemeId", "DBFilterAlias": "BiddingSchemeId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["BudgetBids"]}, {"ReportColumnId": "ReportV12.CampaignStatus", "DBColumnName": "CampaignStatusName", "DBFilterAlias": "CampaignStatusId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData"}, {"ReportColumnId": "Report.CampaignStatus", "DBColumnName": "CampaignStatusName", "DBFilterAlias": "CampaignStatusId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData"}, {"ReportColumnId": "Report.CurrencyCode", "DBColumnName": "CurrencyCode", "DbType": "NChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["MoreBreakdowns"], "AllowDBNull": false}, {"ReportColumnId": "Report.CustomParameters", "DBColumnName": "CustomParameters", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.CustomParametersCampaign", "DBColumnName": "CustomParameters", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.CustomParametersAdGroup", "DBColumnName": "CustomParameters", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.CustomParametersAd", "DBColumnName": "CustomParameters", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.CustomParametersKeyword", "DBColumnName": "CustomParameters", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.CustomParametersProduct", "DBColumnName": "CustomParameters", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.DeliveredMatchType", "DBColumnName": "DeliveredMatchTypeDesc", "DBFilterAlias": "DeliveredMatchTypeId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["BudgetBids"], "FilterType": "DomainData"}, {"ReportColumnId": "ReportPreview.DeliveredMatchType", "DBColumnName": "DeliveredMatchTypeDesc", "DBFilterAlias": "DeliveredMatchTypeId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery", "BudgetBids"], "IsStackedColumn": true}, {"ReportColumnId": "ReportPreviewAC.DeliveredMatchType", "DBColumnName": "MatchTypeDesc", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery", "BudgetBids"]}, {"ReportColumnId": "ACReport.DeliveredMatchType", "DBColumnName": "MatchTypeDesc", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData"}, {"ReportColumnId": "Report.DestinationUrl", "DBColumnName": "DestinationUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.DestinationUrlAd", "DBColumnName": "DestinationUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.DestinationUrlKeyword", "DBColumnName": "DestinationUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.DestinationUrlProduct", "DBColumnName": "DestinationUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.DeviceOS", "DBColumnName": "DeviceOSName", "DBFilterAlias": "DeviceOSId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "FilterType": "DomainData", "IsExcludedForISDimension": true, "Category": ["Delivery"], "IsStackedColumn": true}, {"ReportColumnId": "Report.DeviceType", "DBColumnName": "DeviceTypeName", "DBFilterAlias": "DeviceTypeId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "FilterType": "DomainData", "Category": ["Delivery", "Targeting"], "IsStackedColumn": true}, {"ReportColumnId": "Report.FinalAppUrl", "DBColumnName": "FinalAppUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.FinalMobileUrl", "DBColumnName": "FinalMobileUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.FinalUrl", "DBColumnName": "FinalUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.FinalUrlSuffix", "DBColumnName": "FinalUrlSuffix", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.FinalUrlSuffixCampaign", "DBColumnName": "FinalUrlSuffix", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.FinalUrlSuffixAdGroup", "DBColumnName": "FinalUrlSuffix", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.FinalUrlSuffixAd", "DBColumnName": "FinalUrlSuffix", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.FinalUrlSuffixKeyword", "DBColumnName": "FinalUrlSuffix", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.KeywordId", "DBColumnName": "OrderItemId", "DBFilterAlias": "OrderItemID", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "Integer", "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.Keyword", "DBColumnName": "Keyword", "DBFilterAlias": "Keyword", "ApiFilterAlias": "Keywords", "DbType": "NVarChar", "IdentityColumn": "Report.KeywordId", "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "WrapAndEscapeDbFilterString": true}, {"ReportColumnId": "Report.SearchQuery", "DBColumnName": "SearchQuery", "DBFilterAlias": "SearchQuery", "ApiFilterAlias": "SearchQueries", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"], "WrapAndEscapeDbFilterString": true}, {"ReportColumnId": "Report.SearchCategory", "DBColumnName": "SearchCategory", "DBFilterAlias": "Category", "ApiFilterAlias": "SearchCategory", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"], "WrapAndEscapeDbFilterString": true}, {"ReportColumnId": "Report.SearchVolume", "DbColumnName": "SearchVolume", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Performance"]}, {"ReportColumnId": "Report.AdGroupCriterionId", "DBColumnName": "ProductTargetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.KeywordLabels", "DBColumnName": "KeywordLabels", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.KeywordStatus", "DBColumnName": "KeywordStatusName", "DBFilterAlias": "KeywordStatusId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["AdAttributes"], "IsStackedColumn": true}, {"ReportColumnId": "Report.LandingPageExperience", "DBColumnName": "QBRRating", "DBFilterAlias": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true}, {"ReportColumnId": "Report2.LandingPageExperience", "DBColumnName": "QBRRating", "DBFilterAlias": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.LandingPageExperienceCampaign", "DBColumnName": "QBRRating", "DBFilterAlias": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.LandingPageExperienceAdGroup", "DBColumnName": "QBRRating", "DBFilterAlias": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.LandingPageExperienceKeyword", "DBColumnName": "QBRRating", "DBFilterAlias": "ORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.TopVsOther", "DBColumnName": "PagePositionId2", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Delivery"]}, {"ReportColumnId": "Report.CurrentMaxCpc", "DBColumnName": "MaxCPC", "DbType": "Money", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.ExpectedCtr", "DBColumnName": "PClickRating", "DBFilterAlias": "PClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true}, {"ReportColumnId": "Report2.ExpectedCtr", "DBColumnName": "PClickRating", "DBFilterAlias": "PClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.ExpectedCtrCampaign", "DBColumnName": "PClickRating", "DBFilterAlias": "PClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.ExpectedCtrAdGroup", "DBColumnName": "PClickRating", "DBFilterAlias": "PClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.ExpectedCtrKeyword", "DBColumnName": "PClickRating", "DBFilterAlias": "PClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.FirstPageBid", "DBColumnName": "SidebarBid", "DbType": "Float", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["BudgetBids"]}, {"ReportColumnId": "Report.HistoricalAdRelevance", "DBColumnName": "HistoricalORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true}, {"ReportColumnId": "Report.HistoricalExpectedCtr", "DBColumnName": "HistoricalPClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true}, {"ReportColumnId": "Report.HistoricalLandingPageExperience", "DBColumnName": "HistoricalQBRRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true}, {"ReportColumnId": "Report.HistoricalQualityScore", "DBColumnName": "HistoricalQualityScore", "DBFilterAlias": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalAdRelevance", "DBColumnName": "HistoricalORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalExpectedCtr", "DBColumnName": "HistoricalPClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalLandingPageExperience", "DBColumnName": "HistoricalQBRRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalQualityScore", "DBColumnName": "HistoricalQualityScore", "DBFilterAlias": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalAdRelevanceCampaign", "DBColumnName": "HistoricalORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalExpectedCtrCampaign", "DBColumnName": "HistoricalPClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalLandingPageExperienceCampaign", "DBColumnName": "HistoricalQBRRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalQualityScoreCampaign", "DBColumnName": "HistoricalQualityScore", "DBFilterAlias": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalAdRelevanceAdGroup", "DBColumnName": "HistoricalORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalExpectedCtrAdGroup", "DBColumnName": "HistoricalPClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalLandingPageExperienceAdGroup", "DBColumnName": "HistoricalQBRRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalQualityScoreAdGroup", "DBColumnName": "HistoricalQualityScore", "DBFilterAlias": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalAdRelevanceKeyword", "DBColumnName": "HistoricalORVRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalExpectedCtrKeyword", "DBColumnName": "HistoricalPClickRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalLandingPageExperienceKeyword", "DBColumnName": "HistoricalQBRRating", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.HistoricalQualityScoreKeyword", "DBColumnName": "HistoricalQualityScore", "DBFilterAlias": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.Mainline1Bid", "DBColumnName": "Mainline1Bid", "DbType": "Float", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["BudgetBids"]}, {"ReportColumnId": "Report.MainlineBid", "DBColumnName": "MainlineBid", "DbType": "Float", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["BudgetBids"]}, {"ReportColumnId": "Report.QualityImpact", "DBColumnName": "QualityImpact", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.QualityScore", "DBColumnName": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true}, {"ReportColumnId": "Report2.QualityScore", "DBColumnName": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.QualityScoreCampaign", "DBColumnName": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.QualityScoreAdGroup", "DBColumnName": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report2.QualityScoreKeyword", "DBColumnName": "QualityScore", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsExcludedForISDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.Language", "DBColumnName": "LanguageName", "DBFilterAlias": "LanguageCode", "ApiFilterAlias": "LanguageCode", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "WrapAndEscapeDbFilterString": true, "FilterType": "DomainData", "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "ReportV12.Language", "DBColumnName": "LanguageName", "DBFilterAlias": "LanguageCode", "ApiFilterAlias": "LanguageCode", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "WrapAndEscapeDbFilterString": true, "FilterType": "DomainData", "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.Network", "DBColumnName": "NetworkId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Delivery", "Targeting"], "IsStackedColumn": true}, {"ReportColumnId": "Report.TrackingTemplate", "DBColumnName": "TrackingTemplate", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.TrackingTemplateCampaign", "DBColumnName": "TrackingTemplate", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.TrackingTemplateAdGroup", "DBColumnName": "TrackingTemplate", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.TrackingTemplateAd", "DBColumnName": "TrackingTemplate", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.TrackingTemplateKeyword", "DBColumnName": "TrackingTemplate", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.TrackingTemplateProduct", "DBColumnName": "TrackingTemplate", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.AdTitle", "DBColumnName": "AdTitle", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.AdExtensionId", "DBColumnName": "AdExtensionId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AudienceId", "DBColumnName": "TargetValueId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["Targeting"]}, {"ReportColumnId": "Report.AdExtensionVersion", "DBColumnName": "AdExtensionVersion", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AudienceName", "DBColumnName": "AudienceName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"]}, {"ReportColumnId": "Report.TitlePart1", "DBColumnName": "AdTitle1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.AssociationId", "DBColumnName": "AssociationId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AssociationLevel", "DBColumnName": "AssociationLevel", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AssociationStatus", "DBColumnName": "AssociationStatus", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "IsStackedColumn": true}, {"ReportColumnId": "Report.TitlePart2", "DBColumnName": "AdTitle2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.AudienceType", "DBColumnName": "TargetTypeId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"], "IsStackedColumn": true}, {"ReportColumnId": "Report.TitlePart3", "DBColumnName": "AdTitle3", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.TargetingSetting", "DBColumnName": "TargetSetting", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"]}, {"ReportColumnId": "Report.AdStatus", "DBColumnName": "AdStatusName", "DBFilterAlias": "AdStatusId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery"], "IsStackedColumn": true}, {"ReportColumnId": "Report.AdExtensionType", "DBColumnName": "AdExtensionTypeName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.ClickType", "DBColumnName": "ClickTypeId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Delivery"]}, {"ReportColumnId": "Report.BudgetName", "DBColumnName": "BudgetName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["BudgetBids"]}, {"ReportColumnId": "Report.CampaignLabels", "DBColumnName": "CampaignLabels", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.LabelName", "DBColumnName": "LabelName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"], "CalculationFunction": {"Default": {"FunctionType": "LabelName"}}, "NotAllValuesLocalized": true}, {"ReportColumnId": "Report.LabelNameKeyword", "DBColumnName": "LabelName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "LabelName"}}, "NotAllValuesLocalized": true}, {"ReportColumnId": "Report.LabelNameAccount", "DBColumnName": "LabelName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "LabelName"}}, "NotAllValuesLocalized": true}, {"ReportColumnId": "Report.LabelNameCampaign", "DBColumnName": "LabelName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "LabelName"}}, "NotAllValuesLocalized": true}, {"ReportColumnId": "Report.LabelNameAdGroup", "DBColumnName": "LabelName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "LabelName"}}, "NotAllValuesLocalized": true}, {"ReportColumnId": "Report.LabelNameAd", "DBColumnName": "LabelName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "LabelName"}}, "NotAllValuesLocalized": true}, {"ReportColumnId": "Report.LabelColor", "DBColumnName": "LabelColor", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"], "HiddenFromUI": true}, {"ReportColumnId": "Report.BudgetAssociationStatus", "DBColumnName": "BudgetAssociationStatus", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["BudgetBids"]}, {"ReportColumnId": "Report.BudgetStatus", "DBColumnName": "BudgetStatusName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsStackedColumn": true, "Category": ["BudgetBids"]}, {"ReportColumnId": "Report.CampaignType", "DBColumnName": "CampaignType", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.AdGroupLabels", "DBColumnName": "AdGroupLabels", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.AdDescription", "DBColumnName": "AdDescription", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.AdDescription2", "DBColumnName": "AdDescription2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.AdLabels", "DBColumnName": "AdLabe<PERSON>", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.DisplayUrl", "DBColumnName": "DisplayUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.Path1", "DBColumnName": "Path1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.Path2", "DBColumnName": "Path2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.Headline", "DBColumnName": "ShortHeadline", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.LongHeadline", "DBColumnName": "LongHeadline", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.BusinessName", "DBColumnName": "BusinessName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AdExtensionTypeId", "DBColumnName": "AdExtensionTypeId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AdExtensionPropertyValue", "DBColumnName": "AdExtensionPropertyValue", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.Country", "DBColumnName": "CountryCodeCode", "DBFilterAlias": "CountryCode", "ApiFilterAlias": "CountryCode", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "WrapAndEscapeDbFilterString": true, "FilterType": "DomainData", "Category": ["Delivery", "Location"]}, {"ReportColumnId": "Report.CountryName", "DBColumnName": "CountryName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Delivery", "Location"]}, {"ReportColumnId": "Report.State", "DBColumnName": "SubGeographyName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Delivery", "Location"]}, {"ReportColumnId": "Report.MetroArea", "DBColumnName": "MetroAreaName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting", "Location"]}, {"ReportColumnId": "Report.ProximityTargetLocation", "DBColumnName": "TargetedLocation", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting", "Location"]}, {"ReportColumnId": "Report.Radius", "DBColumnName": "<PERSON><PERSON>", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting", "Location"]}, {"ReportColumnId": "Report.City", "DBColumnName": "CityName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Delivery", "Location"]}, {"ReportColumnId": "Report.Neighborhood", "DBColumnName": "Neighborhood", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting", "Location"]}, {"ReportColumnId": "Report.QueryIntentCountry", "DBColumnName": "QueryIntentCountryName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Delivery"]}, {"ReportColumnId": "Report.QueryIntentState", "DBColumnName": "QueryIntentSubGeographyName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Delivery"]}, {"ReportColumnId": "Report.QueryIntentCity", "DBColumnName": "QueryIntentCityName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting"]}, {"ReportColumnId": "Report.QueryIntentNeighborhood", "DBColumnName": "QueryIntentNeighborhood", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting"]}, {"ReportColumnId": "Report.QueryIntentDMA", "DBColumnName": "QueryIntentMetroAreaName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting"]}, {"ReportColumnId": "Report.County", "DBColumnName": "County", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting", "Location"]}, {"ReportColumnId": "Report.PostalCode", "DBColumnName": "ZipCode", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting", "Location"]}, {"ReportColumnId": "Report.QueryIntentCounty", "DBColumnName": "QueryIntentCounty", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting"]}, {"ReportColumnId": "Report.QueryIntentPostalCode", "DBColumnName": "QueryIntentZipCode", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting"]}, {"ReportColumnId": "Report.LocationId", "DBColumnName": "LocationId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["Targeting", "Location"]}, {"ReportColumnId": "Report.QueryIntentLocationId", "DBColumnName": "QueryIntentLocationId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "DisplayType": "ID", "Category": ["Targeting"]}, {"ReportColumnId": "Report.LocationType", "DBColumnName": "TargetedLocationTypeId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsStackedColumn": true, "Category": ["Location"]}, {"ReportColumnId": "Report.MostSpecificLocation", "DBColumnName": "MostSpecificLocation", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Location"]}, {"ReportColumnId": "Report.CompanyName", "DBColumnName": "CompanyName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting"]}, {"ReportColumnId": "Report.IndustryName", "DBColumnName": "IndustryName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting"]}, {"ReportColumnId": "Report.JobFunctionName", "DBColumnName": "JobFunctionName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Targeting"]}, {"ReportColumnId": "Report.StartTime", "DBColumnName": "CallStartTime", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["CallDetails"]}, {"ReportColumnId": "Report.EndTime", "DBColumnName": "CallEndTime", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["CallDetails"]}, {"ReportColumnId": "Report.AreaCode", "DBColumnName": "AreaCode", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Location"]}, {"ReportColumnId": "CReport.City", "DBColumnName": "City", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Location"]}, {"ReportColumnId": "CReport.State", "DBColumnName": "Region", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Location"]}, {"ReportColumnId": "Report.CallStatus", "DBColumnName": "CallStatusName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsStackedColumn": true}, {"ReportColumnId": "Report.CallTypeName", "DBColumnName": "CallTypeName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsStackedColumn": true}, {"ReportColumnId": "Report.BidAdjustment", "DBColumnName": "BidAdjustment", "DbType": "Decimal", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["BudgetBids"]}, {"ReportColumnId": "Report.DynamicAdTargetId", "DBColumnName": "AutoTargetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.DynamicAdTarget", "DBColumnName": "AutoTarget", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Targeting"], "AllowDBNull": true}, {"ReportColumnId": "Report.DynamicAdTargetStatus", "DBColumnName": "AutoTargetStatusName", "DBFilterAlias": "AutoTargetStatusId", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "FilterType": "DomainData", "Category": ["Delivery"], "IsStackedColumn": true}, {"ReportColumnId": "Report.WebsiteCoverage", "DBColumnName": "WebsiteCoverage", "DbType": "Decimal", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["MoreBreakdowns"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel0", "DBColumnName": "CustomLabel0", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel1", "DBColumnName": "CustomLabel1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel2", "DBColumnName": "CustomLabel2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel3", "DBColumnName": "CustomLabel3", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel4", "DBColumnName": "CustomLabel4", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel0Product", "DBColumnName": "CustomLabel0", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel1Product", "DBColumnName": "CustomLabel1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel2Product", "DBColumnName": "CustomLabel2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel3Product", "DBColumnName": "CustomLabel3", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CustomLabel4Product", "DBColumnName": "CustomLabel4", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.ProductType1", "DBColumnName": "ProductTypeL1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductType2", "DBColumnName": "ProductTypeL2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductType3", "DBColumnName": "ProductTypeL3", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductType4", "DBColumnName": "ProductTypeL4", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductType5", "DBColumnName": "ProductTypeL5", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductCategory1", "DBColumnName": "OfferCategoryL1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductCategory2", "DBColumnName": "OfferCategoryL2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductCategory3", "DBColumnName": "OfferCategoryL3", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductCategory4", "DBColumnName": "OfferCategoryL4", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ProductCategory5", "DBColumnName": "OfferCategoryL5", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.MerchantProductId", "DBColumnName": "MerchantProductId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"], "DisplayType": "ID"}, {"ReportColumnId": "ReportPreview.MerchantProductId", "DBColumnName": "MerchantProductId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.Title", "DBColumnName": "Title", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.Condition", "DBColumnName": "Condition", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.Brand", "DBColumnName": "Brand", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.Price", "DBColumnName": "Price", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.SellerName", "DBColumnName": "<PERSON><PERSON><PERSON><PERSON>", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.OfferLanguage", "DBColumnName": "ProductOfferLanguageName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.CountryOfSale", "DBColumnName": "CountryOfSaleName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "BSCReport.ClickTypeId", "DBColumnName": "ClickTypeId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "BSCReport.ClickType", "DBColumnName": "ClickTypeId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Delivery"]}, {"ReportColumnId": "Report.LocalStoreCode", "DBColumnName": "LocalStoreCode", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.StoreId", "DBColumnName": "ProviderId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "BSCReport.AdGroupCriterionId", "DBColumnName": "NodeId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "ReportWrapped.ProductGroup", "DBColumnName": "ProductGroup", "DbType": "<PERSON><PERSON><PERSON>", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.ProductGroup", "DBColumnName": "ProductGroup", "DbType": "<PERSON><PERSON><PERSON>", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.PartitionType", "DBColumnName": "PartitionType", "DbType": "TINYINT", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "ReportPreview.PartitionType", "DBColumnName": "PartitionType", "DbType": "TINYINT", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "ReportInt.PartitionType", "DBColumnName": "PartitionType", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.SourceHotelId", "DBColumnName": "SourceHotelId", "DBFilterAlias": "SourceHotelId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.CampaignHotelId", "DBColumnName": "CampaignHotelId", "DBFilterAlias": "CampaignHotelId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "DisplayType": "ID"}, {"ReportColumnId": "Report.AdvertiserHotelId", "DBColumnName": "AdvertiserHotelId", "DBFilterAlias": "AdvertiserHotelId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["LevelOfDetail"], "AllowDBNull": false}, {"ReportColumnId": "Report.HotelName", "DBColumnName": "HotelName", "DBFilterAlias": "HotelName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["LevelOfDetail"], "AllowDBNull": false}, {"ReportColumnId": "Report.HotelGroupNodeId", "DBColumnName": "NodeId", "DBFilterAlias": "NodeId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "DisplayType": "ID"}, {"ReportColumnId": "Report.CheckInDate", "DBColumnName": "CheckInDate", "DBFilterAlias": "CheckInDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Performance"], "AllowDBNull": false}, {"ReportColumnId": "Report.CheckInDateDayOfWeek", "DBColumnName": "CheckInDateDayOfWeek", "DBFilterAlias": "CheckInDateDayOfWeek", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Performance"], "AllowDBNull": false}, {"ReportColumnId": "Report.DateType", "DBColumnName": "DateType", "DBFilterAlias": "DateType", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Performance"], "AllowDBNull": true}, {"ReportColumnId": "Report.Category", "DBColumnName": "Category", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["MoreBreakdowns"], "AllowDBNull": true}, {"ReportColumnId": "Report.StarRating", "DBColumnName": "StarRating", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["MoreBreakdowns"], "AllowDBNull": true}, {"ReportColumnId": "Report.LengthOfStay", "DBColumnName": "LengthOfStay", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Performance"], "AllowDBNull": false}, {"ReportColumnId": "Report.AdvancedBookingWindow", "DBColumnName": "AdvancedBookingWindow", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Performance"], "AllowDBNull": false}, {"ReportColumnId": "ReportWrapped.HotelGroup", "DBColumnName": "HotelGroup", "DbType": "<PERSON><PERSON><PERSON>", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["LevelOfDetail"], "AllowDBNull": true}, {"ReportColumnId": "ReportInt.AccountId", "DBColumnName": "AccountId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID"}, {"ReportColumnId": "Report.GoalId", "DBColumnName": "GoalId", "DBFilterAlias": "GoalId", "ApiFilterAlias": "GoalIds", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "Integer", "DisplayType": "ID", "Category": ["Conversions"]}, {"ReportColumnId": "Report.Goal", "DBColumnName": "GoalName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Conversions"], "IsExcludedForISDimension": true}, {"ReportColumnId": "Report.GoalType", "DBColumnName": "GoalTypeId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsExcludedForISDimension": true, "Category": ["Conversions"], "IsStackedColumn": true}, {"ReportColumnId": "Report.GoalCategory", "DBColumnName": "GoalCategory", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsExcludedForISDimension": true, "Category": ["Conversions"], "IsStackedColumn": true}, {"ReportColumnId": "BSCReport.TopVsOther", "DBColumnName": "PagePositionId2", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.Gender", "DBColumnName": "GenderName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"], "IsStackedColumn": true}, {"ReportColumnId": "Report.AgeGroup", "DBColumnName": "AgeBucketDesc", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"], "IsStackedColumn": true}, {"ReportColumnId": "Report.PublisherUrl", "DBColumnName": "PublisherURL", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"]}, {"ReportColumnId": "DSAReport.Headline", "DBColumnName": "DSAAdTitle", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.CategoryList", "DBColumnName": "DSACategoryList", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": false}, {"ReportColumnId": "DSAReport.FinalUrl", "DBColumnName": "DSADestinationURL", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.LandingPageTitle", "DBColumnName": "DSALandingPageTitle", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": false}, {"ReportColumnId": "Report.FeedUrl", "DBColumnName": "FeedURL", "DBFilterAlias": "IsPageURLFilters", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Delivery"]}, {"ReportColumnId": "Report.Category0", "DBColumnName": "TopLevelCategory", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.Category1", "DBColumnName": "FirstLevelCategory", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.Category2", "DBColumnName": "SecondLevelCategory", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.Param1", "DBColumnName": "Param1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.Param2", "DBColumnName": "Param2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.Param3", "DBColumnName": "Param3", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.BaseCampaignId", "DBColumnName": "BaseCampaignId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.NegativeKeyword", "DBColumnName": "NegativeKeyword", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.ConflictLevel", "DBColumnName": "ConflictLevelName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.NegativeKeywordListId", "DBColumnName": "NegativeKeywordListId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.NegativeKeywordList", "DBColumnName": "NegativeKeywordListName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.NegativeKeywordId", "DBColumnName": "NegativeKeywordId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.NegativeKeywordMatchType", "DBColumnName": "NegativeKeywordMatchTypeDesc", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "CHReport.AdGroupName", "DBColumnName": "AdGroupName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.ConflictType", "DBColumnName": "ConflictType", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.DateTime", "DBColumnName": "ModificationDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ModifiedByUser", "DBColumnName": "ModifiedByUser", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.UserCustomerId", "DBColumnName": "UserCustomerId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID"}, {"ReportColumnId": "Report.ChangedBy", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.ModifiedByUser", "Report.UserCustomerId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryMadeBy"}}, "LoadDependentColumnForDimension": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.ItemChanged", "DBColumnName": "EntityId", "DBFilterAlias": "ReportLevelId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AttributeChanged", "DBColumnName": "AttributeId", "DbType": "SmallInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.How<PERSON><PERSON>ed", "DBColumnName": "ChangeType", "DBFilterAlias": "ChangeTypeId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "ReportStr.How<PERSON><PERSON><PERSON>", "DBColumnName": "ChangeType", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.NewValueDB", "DBColumnName": "NewValue", "DbType": "NVarChar", "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.OldValueDB", "DBColumnName": "OldValue", "DbType": "NVarChar", "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.OldValue", "DBColumnName": "OldValue", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.AttributeChanged", "Report.OldValueDB"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryReport", "Parameters": {"AttributeMapping": {"13": {"ReportColumnId": "Report.AdType"}, "28": {"ReportColumnId": "Report.BudgetType"}, "53": {"ReportColumnId": "Report.StatusId"}, "60": {"ReportColumnId": "Report.StatusId"}, "71": {"ReportColumnId": "Report.PreferredLanguageId"}, "75": {"ReportColumnId": "Report.StatusId"}, "77": {"ReportColumnId": "Report.StatusId"}, "98": {"ReportColumnId": "Report.StatusId"}, "144": {"ReportColumnId": "Report.SearchNetworkOption"}, "147": {"ReportColumnId": "Report.AdDistribution"}, "154": {"ReportColumnId": "Report.Gender", "HasMultipeValue": true}, "155": {"ReportColumnId": "Report.AgeGroup", "HasMultipeValue": true}, "156": {"ReportColumnId": "Report.DayName"}, "157": {"ReportColumnId": "Report.HourBucket"}, "158": {"ReportColumnId": "Report.<PERSON>ce", "HasMultipeValue": true}, "159": {"HasMultipeValue": true}, "160": {"HasMultipeValue": true}, "188": {"ReportColumnId": "Report.AdvancedLocationTargeting"}, "189": {"ReportColumnId": "Report.BidMatchType"}, "200": {"ReportColumnId": "Report.DayName", "HasMultipeValue": true}, "210": {"ReportColumnId": "Report.DevicePreferenceId"}, "221": {"ReportColumnId": "Report.DistanceUnit", "HasMultipeValue": true, "LookupIndex": 1}, "259": {"ReportColumnId": "Report.BiddingScheme"}, "264": {"ReportColumnId": "Report.EditorialStatusId"}, "265": {"ReportColumnId": "Report.BudgetPauseType"}, "283": {"ReportColumnId": "Report.UseSearcherTimeZoneId"}, "304": {"ReportColumnId": "Report.StatusId"}, "305": {"ReportColumnId": "Report.TargetingSetting"}, "306": {"ReportColumnId": "Report.TargetingSetting"}, "307": {"ReportColumnId": "Report.TargetingSetting"}, "308": {"ReportColumnId": "Report.TargetingSetting"}, "309": {"ReportColumnId": "Report.TargetingSetting"}, "315": {"HasMultipeValue": true}, "316": {"HasMultipeValue": true}, "317": {"HasMultipeValue": true}, "318": {"HasMultipeValue": true}, "319": {"HasMultipeValue": true}, "320": {"HasMultipeValue": true}, "321": {"HasMultipeValue": true}, "322": {"HasMultipeValue": true}, "323": {"HasMultipeValue": true}, "324": {"HasMultipeValue": true}, "325": {"HasMultipeValue": true}, "326": {"HasMultipeValue": true}, "327": {"HasMultipeValue": true}, "362": {"HasMultipeValue": true}, "363": {"HasMultipeValue": true}, "332": {"ReportColumnId": "Report.TargetingSource"}, "353": {"ReportColumnId": "Report.BidMatchType"}, "357": {"ReportColumnId": "Report.AdvancedLocationTargeting"}, "365": {"ReportColumnId": "Report.AssociationStatus"}, "382": {"ReportColumnId": "Report.AdAssetPinType"}, "385": {"ReportColumnId": "Report.StatusId"}, "491": {"ReportColumnId": "Report.FeedType"}, "494": {"ReportColumnId": "Report.AdScheduleUseSearcherTimeZone"}, "548": {"ReportColumnId": "Report.BusinessAttributes"}, "557": {"ReportColumnId": "Report.DeviceTypesFilter"}, "558": {"ReportColumnId": "Report.CampaignTypesFilter"}, "563": {"ReportColumnId": "Report.FinalURLExpansion"}, "233": {"ReportColumnId": "Report.IsExcluded"}, "573": {"ReportColumnId": "Report.CampaignType"}, "574": {"ReportColumnId": "Report.UpgradeStatus"}, "592": {"ReportColumnId": "Report.NewCustomerAcquisitionBidMode"}, "594": {"ReportColumnId": "Report.UseAccountLevelAdditionalValue"}}}}}}, {"ReportColumnId": "Report.NewValue", "DBColumnName": "NewValue", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.AttributeChanged", "Report.NewValueDB"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryReport", "Parameters": {"AttributeMapping": {"13": {"ReportColumnId": "Report.AdType"}, "28": {"ReportColumnId": "Report.BudgetType"}, "53": {"ReportColumnId": "Report.StatusId"}, "60": {"ReportColumnId": "Report.StatusId"}, "71": {"ReportColumnId": "Report.PreferredLanguageId"}, "75": {"ReportColumnId": "Report.StatusId"}, "77": {"ReportColumnId": "Report.StatusId"}, "98": {"ReportColumnId": "Report.StatusId"}, "144": {"ReportColumnId": "Report.SearchNetworkOption"}, "147": {"ReportColumnId": "Report.AdDistribution"}, "154": {"ReportColumnId": "Report.Gender", "HasMultipeValue": true}, "155": {"ReportColumnId": "Report.AgeGroup", "HasMultipeValue": true}, "156": {"ReportColumnId": "Report.DayName"}, "157": {"ReportColumnId": "Report.HourBucket"}, "158": {"ReportColumnId": "Report.<PERSON>ce", "HasMultipeValue": true}, "159": {"HasMultipeValue": true}, "160": {"HasMultipeValue": true}, "188": {"ReportColumnId": "Report.AdvancedLocationTargeting"}, "189": {"ReportColumnId": "Report.BidMatchType"}, "200": {"ReportColumnId": "Report.DayName", "HasMultipeValue": true}, "210": {"ReportColumnId": "Report.DevicePreferenceId"}, "221": {"ReportColumnId": "Report.DistanceUnit", "HasMultipeValue": true, "LookupIndex": 1}, "259": {"ReportColumnId": "Report.BiddingScheme"}, "264": {"ReportColumnId": "Report.EditorialStatusId"}, "265": {"ReportColumnId": "Report.BudgetPauseType"}, "283": {"ReportColumnId": "Report.UseSearcherTimeZoneId"}, "304": {"ReportColumnId": "Report.StatusId"}, "305": {"ReportColumnId": "Report.TargetingSetting"}, "306": {"ReportColumnId": "Report.TargetingSetting"}, "307": {"ReportColumnId": "Report.TargetingSetting"}, "308": {"ReportColumnId": "Report.TargetingSetting"}, "309": {"ReportColumnId": "Report.TargetingSetting"}, "315": {"HasMultipeValue": true}, "316": {"HasMultipeValue": true}, "317": {"HasMultipeValue": true}, "318": {"HasMultipeValue": true}, "319": {"HasMultipeValue": true}, "320": {"HasMultipeValue": true}, "321": {"HasMultipeValue": true}, "322": {"HasMultipeValue": true}, "323": {"HasMultipeValue": true}, "324": {"HasMultipeValue": true}, "325": {"HasMultipeValue": true}, "326": {"HasMultipeValue": true}, "327": {"HasMultipeValue": true}, "362": {"HasMultipeValue": true}, "363": {"HasMultipeValue": true}, "332": {"ReportColumnId": "Report.TargetingSource"}, "353": {"ReportColumnId": "Report.BidMatchType"}, "357": {"ReportColumnId": "Report.AdvancedLocationTargeting"}, "365": {"ReportColumnId": "Report.AssociationStatus"}, "382": {"ReportColumnId": "Report.AdAssetPinType"}, "385": {"ReportColumnId": "Report.StatusId"}, "491": {"ReportColumnId": "Report.FeedType"}, "494": {"ReportColumnId": "Report.AdScheduleUseSearcherTimeZone"}, "548": {"ReportColumnId": "Report.BusinessAttributes"}, "557": {"ReportColumnId": "Report.DeviceTypesFilter"}, "558": {"ReportColumnId": "Report.CampaignTypesFilter"}, "563": {"ReportColumnId": "Report.FinalURLExpansion"}, "233": {"ReportColumnId": "Report.IsExcluded"}, "573": {"ReportColumnId": "Report.CampaignType"}, "574": {"ReportColumnId": "Report.UpgradeStatus"}, "592": {"ReportColumnId": "Report.NewCustomerAcquisitionBidMode"}, "594": {"ReportColumnId": "Report.UseAccountLevelAdditionalValue"}}}}}}, {"ReportColumnId": "ReportPreview.OldValue", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.AttributeChanged", "Report.OldValueDB"], "IsBIColumn": false, "IsISColumn": false, "Category": ["LevelOfDetail"], "AllowDBNull": true, "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryReport", "Parameters": {"AttributeMapping": {"13": {"ReportColumnId": "Report.AdType"}, "28": {"ReportColumnId": "Report.BudgetType"}, "53": {"ReportColumnId": "Report.StatusId"}, "60": {"ReportColumnId": "Report.StatusId"}, "71": {"ReportColumnId": "Report.PreferredLanguageId"}, "75": {"ReportColumnId": "Report.StatusId"}, "77": {"ReportColumnId": "Report.StatusId"}, "98": {"ReportColumnId": "Report.StatusId"}, "144": {"ReportColumnId": "Report.SearchNetworkOption"}, "147": {"ReportColumnId": "Report.AdDistribution"}, "154": {"ReportColumnId": "Report.Gender", "HasMultipeValue": true}, "155": {"ReportColumnId": "Report.AgeGroup", "HasMultipeValue": true}, "156": {"ReportColumnId": "Report.DayName"}, "157": {"ReportColumnId": "Report.HourBucket"}, "158": {"ReportColumnId": "Report.<PERSON>ce", "HasMultipeValue": true}, "159": {"HasMultipeValue": true}, "160": {"HasMultipeValue": true}, "188": {"ReportColumnId": "Report.AdvancedLocationTargeting"}, "189": {"ReportColumnId": "Report.BidMatchType"}, "200": {"ReportColumnId": "Report.DayName", "HasMultipeValue": true}, "210": {"ReportColumnId": "Report.DevicePreferenceId"}, "221": {"ReportColumnId": "Report.DistanceUnit", "HasMultipeValue": true, "LookupIndex": 1}, "259": {"ReportColumnId": "Report.BiddingScheme"}, "264": {"ReportColumnId": "Report.EditorialStatusId"}, "265": {"ReportColumnId": "Report.BudgetPauseType"}, "283": {"ReportColumnId": "Report.UseSearcherTimeZoneId"}, "304": {"ReportColumnId": "Report.StatusId"}, "305": {"ReportColumnId": "Report.TargetingSetting"}, "306": {"ReportColumnId": "Report.TargetingSetting"}, "307": {"ReportColumnId": "Report.TargetingSetting"}, "308": {"ReportColumnId": "Report.TargetingSetting"}, "309": {"ReportColumnId": "Report.TargetingSetting"}, "315": {"HasMultipeValue": true}, "316": {"HasMultipeValue": true}, "317": {"HasMultipeValue": true}, "318": {"HasMultipeValue": true}, "319": {"HasMultipeValue": true}, "320": {"HasMultipeValue": true}, "321": {"HasMultipeValue": true}, "322": {"HasMultipeValue": true}, "323": {"HasMultipeValue": true}, "324": {"HasMultipeValue": true}, "325": {"HasMultipeValue": true}, "326": {"HasMultipeValue": true}, "327": {"HasMultipeValue": true}, "362": {"HasMultipeValue": true}, "363": {"HasMultipeValue": true}, "332": {"ReportColumnId": "Report.TargetingSource"}, "353": {"ReportColumnId": "Report.BidMatchType"}, "357": {"ReportColumnId": "Report.AdvancedLocationTargeting"}, "365": {"ReportColumnId": "Report.AssociationStatus"}, "382": {"ReportColumnId": "Report.AdAssetPinType"}, "385": {"ReportColumnId": "Report.StatusId"}, "491": {"ReportColumnId": "Report.FeedType"}, "494": {"ReportColumnId": "Report.AdScheduleUseSearcherTimeZone"}, "548": {"ReportColumnId": "Report.BusinessAttributes"}, "557": {"ReportColumnId": "Report.DeviceTypesFilter"}, "558": {"ReportColumnId": "Report.CampaignTypesFilter"}, "563": {"ReportColumnId": "Report.FinalURLExpansion"}, "233": {"ReportColumnId": "Report.IsExcluded"}, "573": {"ReportColumnId": "Report.CampaignType"}, "574": {"ReportColumnId": "Report.UpgradeStatus"}, "592": {"ReportColumnId": "Report.NewCustomerAcquisitionBidMode"}, "594": {"ReportColumnId": "Report.UseAccountLevelAdditionalValue"}}}}}, "LoadDependentColumnForDimension": true}, {"ReportColumnId": "ReportPreview.NewValue", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.AttributeChanged", "Report.NewValueDB"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryReport", "Parameters": {"AttributeMapping": {"13": {"ReportColumnId": "Report.AdType"}, "28": {"ReportColumnId": "Report.BudgetType"}, "53": {"ReportColumnId": "Report.StatusId"}, "60": {"ReportColumnId": "Report.StatusId"}, "71": {"ReportColumnId": "Report.PreferredLanguageId"}, "75": {"ReportColumnId": "Report.StatusId"}, "77": {"ReportColumnId": "Report.StatusId"}, "98": {"ReportColumnId": "Report.StatusId"}, "144": {"ReportColumnId": "Report.SearchNetworkOption"}, "147": {"ReportColumnId": "Report.AdDistribution"}, "154": {"ReportColumnId": "Report.Gender", "HasMultipeValue": true}, "155": {"ReportColumnId": "Report.AgeGroup", "HasMultipeValue": true}, "156": {"ReportColumnId": "Report.DayName"}, "157": {"ReportColumnId": "Report.HourBucket"}, "158": {"ReportColumnId": "Report.<PERSON>ce", "HasMultipeValue": true}, "159": {"HasMultipeValue": true}, "160": {"HasMultipeValue": true}, "188": {"ReportColumnId": "Report.AdvancedLocationTargeting"}, "189": {"ReportColumnId": "Report.BidMatchType"}, "200": {"ReportColumnId": "Report.DayName", "HasMultipeValue": true}, "210": {"ReportColumnId": "Report.DevicePreferenceId"}, "221": {"ReportColumnId": "Report.DistanceUnit", "HasMultipeValue": true, "LookupIndex": 1}, "259": {"ReportColumnId": "Report.BiddingScheme"}, "264": {"ReportColumnId": "Report.EditorialStatusId"}, "265": {"ReportColumnId": "Report.BudgetPauseType"}, "283": {"ReportColumnId": "Report.UseSearcherTimeZoneId"}, "304": {"ReportColumnId": "Report.StatusId"}, "305": {"ReportColumnId": "Report.TargetingSetting"}, "306": {"ReportColumnId": "Report.TargetingSetting"}, "307": {"ReportColumnId": "Report.TargetingSetting"}, "308": {"ReportColumnId": "Report.TargetingSetting"}, "309": {"ReportColumnId": "Report.TargetingSetting"}, "315": {"HasMultipeValue": true}, "316": {"HasMultipeValue": true}, "317": {"HasMultipeValue": true}, "318": {"HasMultipeValue": true}, "319": {"HasMultipeValue": true}, "320": {"HasMultipeValue": true}, "321": {"HasMultipeValue": true}, "322": {"HasMultipeValue": true}, "323": {"HasMultipeValue": true}, "324": {"HasMultipeValue": true}, "325": {"HasMultipeValue": true}, "326": {"HasMultipeValue": true}, "327": {"HasMultipeValue": true}, "362": {"HasMultipeValue": true}, "363": {"HasMultipeValue": true}, "332": {"ReportColumnId": "Report.TargetingSource"}, "353": {"ReportColumnId": "Report.BidMatchType"}, "357": {"ReportColumnId": "Report.AdvancedLocationTargeting"}, "365": {"ReportColumnId": "Report.AssociationStatus"}, "382": {"ReportColumnId": "Report.AdAssetPinType"}, "385": {"ReportColumnId": "Report.StatusId"}, "491": {"ReportColumnId": "Report.FeedType"}, "494": {"ReportColumnId": "Report.AdScheduleUseSearcherTimeZone"}, "548": {"ReportColumnId": "Report.BusinessAttributes"}, "557": {"ReportColumnId": "Report.DeviceTypesFilter"}, "558": {"ReportColumnId": "Report.CampaignTypesFilter"}, "563": {"ReportColumnId": "Report.FinalURLExpansion"}, "233": {"ReportColumnId": "Report.IsExcluded"}, "573": {"ReportColumnId": "Report.CampaignType"}, "574": {"ReportColumnId": "Report.UpgradeStatus"}, "592": {"ReportColumnId": "Report.NewCustomerAcquisitionBidMode"}, "594": {"ReportColumnId": "Report.UseAccountLevelAdditionalValue"}}}}}, "LoadDependentColumnForDimension": true}, {"ReportColumnId": "Report.EntityName", "DBColumnName": "EntityValue", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail", "MoreBreakdowns"]}, {"ReportColumnId": "Report.EntityId", "DBColumnName": "EntityValueId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail", "MoreBreakdowns"]}, {"ReportColumnId": "Report.ApplicationId", "DBColumnName": "ApplicationId", "DbType": "TinyInt", "IdentityColumn": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Tool", "DBColumnName": "ApplicationId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": ["Report.ApplicationId", "Report.ModifiedByUser"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryTool"}}}, {"ReportColumnId": "ReportPreview.Tool", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": ["Report.ApplicationId", "Report.ModifiedByUser"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryTool"}}, "LoadDependentColumnForDimension": true}, {"ReportColumnId": "Report.PreferredLanguageId", "DBColumnName": "PreferredLanguageId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.HourBucket", "DBColumnName": "HourBucket", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.EditorialStatusId", "DBColumnName": "EditorialStatusId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.StatusId", "DBColumnName": "StatusId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.BudgetType", "DBColumnName": "BudgetType", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsStackedColumn": true}, {"ReportColumnId": "Report.BudgetPauseType", "DBColumnName": "BudgetPauseType", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.BiddingScheme", "DBColumnName": "BiddingScheme", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.DayName", "DBColumnName": "WeekDays", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.UseSearcherTimeZoneId", "DBColumnName": "UseSearcherTimeZoneId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.DevicePreferenceId", "DBColumnName": "DevicePreferenceId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.AdvancedLocationTargeting", "DBColumnName": "AdvancedLocationTargeting", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.TargetingSource", "DBColumnName": "TargetingSource", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.SearchNetworkOption", "DBColumnName": "SearchNetworkOption", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.AdAssetPinType", "DBColumnName": "AdAssetPinType", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsStackedColumn": true}, {"ReportColumnId": "Report.DistanceUnit", "DBColumnName": "DistanceUnit", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.<PERSON>ce", "DBColumnName": "<PERSON><PERSON>", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.DeviceTypesFilter", "DBColumnName": "DeviceTypesFilter", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.CampaignTypesFilter", "DBColumnName": "CampaignTypesFilter", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.ChangeMadeTo", "DBColumnName": "UserEmail", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ModifiedByUserEmail", "DBColumnName": "ModifiedByUserEmail", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.ChangeMadeBy", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.ModifiedByUserEmail", "Report.UserCustomerId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"], "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryMadeBy"}}}, {"ReportColumnId": "ReportPreview.ChangeMadeBy", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.ModifiedByUserEmail", "Report.UserCustomerId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "ChangeHistoryMadeBy"}}, "LoadDependentColumnForDimension": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.ChangeTotalCount", "DBColumnName": "TotalCount", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.UserId", "DBColumnName": "UserId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"], "DisplayType": "ID"}, {"ReportColumnId": "Report.FeedType", "DBColumnName": "FeedType", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsStackedColumn": true}, {"ReportColumnId": "Report.FinalURLExpansion", "DBColumnName": "FinalURLExpansion", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.IsExcluded", "DBColumnName": "IsExcluded", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.UpgradeStatus", "DBColumnName": "UpgradeStatus", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.AdScheduleUseSearcherTimeZone", "DBColumnName": "AdScheduleUseSearcherTimeZone", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.ProductBought", "DBColumnName": "PurchasedGTIN", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.AdGroupType", "DBColumnName": "AdGroupTypeId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"], "IsStackedColumn": true}, {"ReportColumnId": "Report.AdGroupTypePreview", "DBColumnName": "AdGroupTypeId", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["LevelOfDetail"], "AllowDBNull": false}, {"ReportColumnId": "Report.AdScenarioType", "DBColumnName": "AdScenarioType", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Delivery"], "AllowDBNull": false}, {"ReportColumnId": "Report.VerticalAdType", "DBColumnName": "AdScenarioType", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "HiddenFromUI": true}, {"ReportColumnId": "Report.BusinessAttributes", "DBColumnName": "BusinessAttributes", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.Description", "DBColumnName": "Description", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["ProductAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.GTIN", "DBColumnName": "GTIN", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail", "MoreBreakdowns"]}, {"ReportColumnId": "Report.MPN", "DBColumnName": "MPN", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["LevelOfDetail"], "AllowDBNull": true}, {"ReportColumnId": "Report.ProductBoughtTitle", "DBColumnName": "ProductBoughtTitle", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["LevelOfDetail"], "AllowDBNull": true}, {"ReportColumnId": "ReportComputed.AccountName", "DBColumnName": "Account<PERSON><PERSON>", "DbType": "NVarChar", "IdentityColumn": "Report.AccountId", "DependentColumns": ["Report.AccountId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "AccountEntity"}}}, {"ReportColumnId": "ReportComputed.AccountNumber", "DBColumnName": "AccountNumber", "DbType": "NVarChar", "IdentityColumn": "Report.AccountId", "DependentColumns": ["Report.AccountId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "AccountEntity"}}}, {"ReportColumnId": "ReportComputed.DummyShadowDimension", "DBColumnName": "DummyShadowDimension", "DbType": "BigInt", "IdentityColumn": "Report.AccountId", "DependentColumns": ["Report.AccountId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsShadow": true, "HiddenFromUI": true, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "AccountEntity"}}}, {"ReportColumnId": "ReportComputed.AccountStatus", "DBColumnName": "AccountStatusName", "DBFilterAlias": "AccountStatusId", "DbType": "VarChar", "IdentityColumn": "Report.AccountId", "DependentColumns": ["Report.AccountId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery"], "IsStackedColumn": true, "CalculationFunction": {"Default": {"FunctionType": "AccountEntity"}}}, {"ReportColumnId": "ReportComputed.CurrencyCode", "DBColumnName": "CurrencyCode", "DbType": "NChar", "IdentityColumn": "Report.AccountId", "DependentColumns": ["Report.AccountId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"], "CalculationFunction": {"Default": {"FunctionType": "AccountEntity"}}}, {"ReportColumnId": "ReportComputed.CustomerId", "DBColumnName": "CustomerId", "DbType": "Int", "IdentityColumn": "Report.AccountId", "DependentColumns": ["Report.AccountId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "AccountEntity"}}, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "ReportComputed.CustomerName", "DBColumnName": "CustomerName", "DbType": "NVarChar", "IdentityColumn": "Report.AccountId", "DependentColumns": ["Report.AccountId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "AccountEntity"}}}, {"ReportColumnId": "ReportComputed.CampaignName", "DBColumnName": "CampaignName", "DbType": "NVarChar", "IdentityColumn": "Report.CampaignId", "DependentColumns": ["Report.CampaignId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"], "CalculationFunction": {"Default": {"FunctionType": "CampaignEntity"}}}, {"ReportColumnId": "ReportComputed.CampaignStatus", "DBColumnName": "CampaignStatusName", "DBFilterAlias": "CampaignStatusId", "DbType": "NVarChar", "IdentityColumn": "Report.CampaignId", "DependentColumns": ["Report.CampaignId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery"], "IsStackedColumn": true, "CalculationFunction": {"Default": {"FunctionType": "CampaignEntity"}}}, {"ReportColumnId": "ReportComputed.CampaignType", "DBColumnName": "CampaignType", "DbType": "int", "IdentityColumn": "Report.CampaignId", "DependentColumns": ["Report.CampaignId"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsStackedColumn": true, "Category": ["MoreBreakdowns"], "CalculationFunction": {"Default": {"FunctionType": "CampaignEntity"}}}, {"ReportColumnId": "ReportComputed.GregorianDate", "DBColumnName": "GregorianDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsTimeColumn": true, "Category": ["Time"]}, {"ReportColumnId": "ReportComputed.Week", "DBColumnName": "WeekStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true, "Category": ["Time"], "IsStackedColumn": true}, {"ReportColumnId": "ReportComputed.WeekStartingMonday", "DBColumnName": "WeekStartDateMonday", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true, "Category": ["Time"]}, {"ReportColumnId": "ReportComputed.Month", "DBColumnName": "MonthStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true, "Category": ["Time"], "IsStackedColumn": true}, {"ReportColumnId": "ReportComputed.Quarter", "DBColumnName": "QuarterStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true, "Category": ["Time"], "IsStackedColumn": true}, {"ReportColumnId": "ReportComputed.Year", "DBColumnName": "YearNum", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true, "Category": ["Time"], "IsStackedColumn": true}, {"ReportColumnId": "ReportComputed.HourOfDay", "DBColumnName": "HourOfDay", "DbType": "TinyInt", "IdentityColumn": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "IsTimeColumn": true, "Category": ["Time"], "IsStackedColumn": true}, {"ReportColumnId": "ReportComputed.DayOfWeek", "DBColumnName": "DayOfWeek", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true, "Category": ["Time"], "IsStackedColumn": true}, {"ReportColumnId": "ReportNullableComputed.GregorianDate", "DBColumnName": "GregorianDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsTimeColumn": true}, {"ReportColumnId": "ReportNullableComputed.Week", "DBColumnName": "WeekStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true}, {"ReportColumnId": "ReportNullableComputed.WeekStartingMonday", "DBColumnName": "WeekStartDateMonday", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true}, {"ReportColumnId": "ReportNullableComputed.Month", "DBColumnName": "MonthStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true}, {"ReportColumnId": "ReportNullableComputed.Quarter", "DBColumnName": "QuarterStartDate", "DbType": "DateTime", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true}, {"ReportColumnId": "ReportNullableComputed.Year", "DBColumnName": "YearNum", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true}, {"ReportColumnId": "ReportNullableComputed.HourOfDay", "DBColumnName": "HourOfDay", "DbType": "TinyInt", "IdentityColumn": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsTimeColumn": true}, {"ReportColumnId": "ReportNullableComputed.DayOfWeek", "DBColumnName": "DayOfWeek", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "CalculationFunction": {"Default": {"FunctionType": "DateCalculator"}}, "IsTimeColumn": true}, {"ReportColumnId": "DSAReport.AdId", "DBColumnName": "AdId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "DisplayType": "ID"}, {"ReportColumnId": "UCMReport.IncrementalBudgetAmount", "DBColumnName": "IncrementalBudgetAmt", "DbType": "Decimal", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true, "Category": ["BudgetBids"]}, {"ReportColumnId": "UCMReport.DailyBudgetAmount", "DBColumnName": "DailyBudgetAmt", "DbType": "Decimal", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "HiddenFromUI": true, "Category": ["BudgetBids"]}, {"ReportColumnId": "UCMReport.BudgetId", "DBColumnName": "BudgetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "HiddenFromUI": true, "Category": ["BudgetBids"]}, {"ReportColumnId": "Report.IsMainConversionGoal", "DbColumnName": "IsMainConversionGoal", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Conversions"]}, {"ReportColumnId": "Report.msxPublisher", "DbColumnName": "Publisher", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Delivery"], "AllowDBNull": false}, {"ReportColumnId": "Report.msxProvider", "DbColumnName": "Provider", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Delivery"], "AllowDBNull": false}, {"ReportColumnId": "Report.msxDevice", "DbColumnName": "<PERSON><PERSON>", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Delivery"], "AllowDBNull": false}, {"ReportColumnId": "Report.msxDeliveredCountry", "DbColumnName": "DeliveredCountry", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Delivery"], "AllowDBNull": false}, {"ReportColumnId": "Report.msxDeliveredMarket", "DbColumnName": "DeliveredMarket", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Delivery"], "AllowDBNull": false}, {"ReportColumnId": "Report.msxPlacementId", "DbColumnName": "PlacementId", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Delivery"]}, {"ReportColumnId": "Report.msxPageType", "DbColumnName": "PageType", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Delivery"], "AllowDBNull": false}, {"ReportColumnId": "Report.msxRegion", "DbColumnName": "Region", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Delivery"], "AllowDBNull": false}, {"ReportColumnId": "Report.SiteType", "DBColumnName": "SiteType", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Delivery"], "IsStackedColumn": true}, {"ReportColumnId": "Report.UserCountry", "DBColumnName": "UserCountryCode", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Delivery", "Location"]}, {"ReportColumnId": "Report.AssetGroupStatus", "DBColumnName": "AssetGroupStatusName", "DBFilterAlias": "AssetGroupStatusId", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "FilterType": "DomainData", "Category": ["Delivery"], "IsStackedColumn": true}, {"ReportColumnId": "Report.AssetGroupName", "DBColumnName": "AssetGroupName", "DBFilterAlias": "AssetGroupName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AssetGroupId", "DBColumnName": "AssetGroupId", "DBFilterAlias": "AssetGroupId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.Genre", "DBColumnName": "Genre", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Targeting"], "AllowDBNull": false}, {"ReportColumnId": "Report.AdStrengthJSON", "DbColumnName": "AdStrengthJSON", "DbType": "<PERSON><PERSON><PERSON>", "IdentityColumn": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsConversionColumn": false}, {"ReportColumnId": "Report.AdStrength", "DbColumnName": "AdStrength", "DbType": "<PERSON><PERSON><PERSON>", "IdentityColumn": null, "DependentColumns": ["Report.AdStrengthJSON"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsConversionColumn": false, "Category": ["Quality"], "CalculationFunction": {"Default": {"FunctionType": "AdStrengthParser"}}, "LoadDependentColumnForDimension": true}, {"ReportColumnId": "Report.AdStrengthActionItems", "DbColumnName": "AdStrengthActionItems", "DbType": "<PERSON><PERSON><PERSON>", "IdentityColumn": null, "DependentColumns": ["Report.AdStrengthJSON"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "IsConversionColumn": false, "Category": ["Quality"], "CalculationFunction": {"Default": {"FunctionType": "AdStrengthActionItemsParser"}}, "LoadDependentColumnForDimension": true}, {"ReportColumnId": "Report.CollectionId", "DBColumnName": "CollectionId", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.PlacementName", "DBColumnName": "AdUnitId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Delivery"]}, {"ReportColumnId": "Report.AssetId", "DBColumnName": "AssetId", "DBFilterAlias": "AssetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "DisplayType": "ID", "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.<PERSON>set<PERSON><PERSON>nt", "DBColumnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.Headline1AssetId", "DBColumnName": "Headline1AssetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Headline2AssetId", "DBColumnName": "Headline2AssetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Headline3AssetId", "DBColumnName": "Headline3AssetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Description1AssetId", "DBColumnName": "Description1AssetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Description2AssetId", "DBColumnName": "Description2AssetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.LongHeadlineAssetId", "DBColumnName": "LongHeadlineAssetId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.ImageId", "DBColumnName": "ImageId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.LogoImageId", "DBColumnName": "LogoImageId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Headline1Internal", "DBColumnName": "Headline1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Headline1", "DBColumnName": "Headline1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.Headline1AssetId", "Report.Headline1Internal"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "CombinationAsset<PERSON><PERSON><PERSON>"}}}, {"ReportColumnId": "Report.Headline2Internal", "DBColumnName": "Headline2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Headline2", "DBColumnName": "Headline2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.Headline2AssetId", "Report.Headline2Internal"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "CombinationAsset<PERSON><PERSON><PERSON>"}}}, {"ReportColumnId": "Report.Headline3Internal", "DBColumnName": "Headline3", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Headline3", "DBColumnName": "Headline3", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.Headline3AssetId", "Report.Headline3Internal"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "CombinationAsset<PERSON><PERSON><PERSON>"}}}, {"ReportColumnId": "Report.Description1Internal", "DBColumnName": "Description1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Description1", "DBColumnName": "Description1", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.Description1AssetId", "Report.Description1Internal"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "CombinationAsset<PERSON><PERSON><PERSON>"}}}, {"ReportColumnId": "Report.Description2Internal", "DBColumnName": "Description2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Description2", "DBColumnName": "Description2", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.Description2AssetId", "Report.Description2Internal"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "CombinationAsset<PERSON><PERSON><PERSON>"}}}, {"ReportColumnId": "Report.LongHeadlineInternal", "DBColumnName": "LongHeadline", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.CombinationLongHeadline", "DBColumnName": "LongHeadline", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.LongHeadlineAssetId", "Report.LongHeadlineInternal"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "CombinationAsset<PERSON><PERSON><PERSON>"}}}, {"ReportColumnId": "Report.ImageInternal", "DBColumnName": "Image", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Image", "DBColumnName": "Image", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.ImageId", "Report.ImageInternal"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "CombinationAsset<PERSON><PERSON><PERSON>"}}}, {"ReportColumnId": "Report.LogoInternal", "DBColumnName": "Logo", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true}, {"ReportColumnId": "Report.Logo", "DBColumnName": "Logo", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": ["Report.LogoImageId", "Report.LogoInternal"], "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "CalculationFunction": {"Default": {"FunctionType": "CombinationAsset<PERSON><PERSON><PERSON>"}}}, {"ReportColumnId": "Report.AssetType", "DBColumnName": "AssetType", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["AdAttributes"], "AllowDBNull": true}, {"ReportColumnId": "Report.AssetSource", "DBColumnName": "AssetSource", "DbType": "Int", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["AdAttributes"], "IsStackedColumn": true}, {"ReportColumnId": "Report.FeedLabel", "DbColumnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["MoreBreakdowns"]}, {"ReportColumnId": "Report.L1Vertical", "DBColumnName": "ICEL1Id", "DBFilterAlias": "ICEL1Id", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"], "FilterType": "DomainData"}, {"ReportColumnId": "Report.L2Vertical", "DBColumnName": "ICEL2Id", "DBFilterAlias": "ICEL2Id", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"], "FilterType": "DomainData"}, {"ReportColumnId": "Report.L3Vertical", "DBColumnName": "ICEL3Id", "DBFilterAlias": "ICEL3Id", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["MoreBreakdowns"], "FilterType": "DomainData"}, {"ReportColumnId": "Report.NewCustomerAcquisitionBidMode", "DBColumnName": "NewCustomerAcquisitionBidMode", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.UseAccountLevelAdditionalValue", "DBColumnName": "UseAccountLevelAdditionalValue", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false}, {"ReportColumnId": "Report.Region", "DbColumnName": "Region", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Location"], "AllowDBNull": true}, {"ReportColumnId": "Report.POSCountry", "DBColumnName": "POSCountry", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Location"]}, {"ReportColumnId": "Report.POSRegion", "DbColumnName": "POSRegion", "DbType": "VarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "Category": ["Location"], "AllowDBNull": true}, {"ReportColumnId": "Report.POSCity", "DBColumnName": "POSCity", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["Delivery", "Location"]}, {"ReportColumnId": "Report.AppName", "DBColumnName": "AppName", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AppBundle", "DBColumnName": "AppBundle", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.AppStoreUrl", "DBColumnName": "AppStoreUrl", "DbType": "NVarChar", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": true, "Category": ["LevelOfDetail"]}, {"ReportColumnId": "Report.Content", "DBColumnName": "TargetValueId", "DbType": "BigInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"]}, {"ReportColumnId": "Report.ContentType", "DBColumnName": "TargetTypeId", "DbType": "TinyInt", "IdentityColumn": null, "DependentColumns": null, "IsBIColumn": false, "IsISColumn": false, "AllowDBNull": false, "Category": ["Targeting"]}]