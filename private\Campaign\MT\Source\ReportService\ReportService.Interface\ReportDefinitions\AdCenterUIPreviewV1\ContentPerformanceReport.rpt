{"ReportType": "ContentPerformanceReport", "Vertical": "AdCenter", "Columns": {"Report.Content": {}, "Report.ContentType": {}, "ReportComputed.CustomerId": {}, "ReportComputed.CustomerName": {}, "ReportComputed.AccountName": {}, "Report.AccountId": {}, "ReportComputed.AccountNumber": {}, "Report.AdGroupStatus": {}, "ReportComputed.CampaignName": {}, "ReportComputed.CampaignType": {}, "Report.CampaignId": {}, "Report.AdGroupName": {}, "Report.AdGroupId": {}, "Report.PublisherUrl": {}, "Report.Impressions": {}, "Report.Clicks": {}, "Report.Ctr": {}, "Report.AverageCpc": {}, "Report.Spend": {}, "Report.AveragePosition": {}, "ReportNew.AbsoluteTopImpressionRatePercent": {}, "ReportNew.TopImpressionRatePercent": {}, "Report.Conversions": {}, "Report.ConversionsQualified": {}, "Report.ConversionRate": {}, "Report.ConversionRateQualified": {}, "Report.CostPerConversion": {}, "Report.CostPerConversionQualified": {}, "Report.Language": {}, "Report.BidMatchType": {}, "ReportPreview.DeliveredMatchType": {}, "Report.Network": {}, "Report.DeviceType": {}, "Report.DeviceOS": {}, "Report.Assists": {}, "Report.Revenue": {}, "ReportPreview.ReturnOnAdSpend": {}, "Report.CostPerAssist": {}, "ReportPreview.RevenuePerConversion": {}, "Report2.RevenuePerAssist": {}, "ReportComputed.AccountStatus": {}, "ReportComputed.CampaignStatus": {}, "Report.AverageCPM": {}, "Report.AllConversions": {}, "Report.AllConversionsQualified": {}, "Report.AllRevenue": {}, "Report.AllCostPerConversion": {}, "Report.AllCostPerConversionQualified": {}, "Report.AllConversionRate": {}, "Report.AllConversionRateQualified": {}, "Report.AllRevenuePerConversion": {}, "Report.AllReturnOnAdSpend": {}, "Report.ViewThroughConversions": {}, "Report.ViewThroughConversionsQualified": {}, "Report.ViewThroughRevenue": {}, "Report.ViewThroughCostPerConversion": {}, "Report.ViewThroughCostPerConversionQualified": {}, "Report.ViewThroughReturnOnAdSpend": {}, "Report.ViewThroughConversionRate": {}, "Report.ViewThroughConversionRateQualified": {}, "Report.VideoViews": {}, "Report.ViewThroughRate": {}, "Report.AverageCPV": {}, "Report.TotalWatchTimeInMS": {}, "Report.AverageWatchTimePerImpression": {}, "Report.AverageWatchTimePerVideoView": {}, "Report.VideoViewsAt25Percent": {}, "Report.VideoViewsAt50Percent": {}, "Report.VideoViewsAt75Percent": {}, "Report.CompletedVideoViews": {}, "ReportPreview.VideoCompletionRate": {}, "Report.VideoStarts": {}, "Report.VideoSkips": {}, "Report.CPCV": {}, "Report.RevenueAdjustment": {}, "Report.AllRevenueAdjustment": {}, "Report.ViewThroughRevenueAdjustment": {}, "ReportComputed.HourOfDay": {"NotFilterable": true}, "ReportComputed.GregorianDate": {"NotFilterable": true}, "ReportComputed.Week": {"NotFilterable": true}, "ReportComputed.WeekStartingMonday": {"NotFilterable": true}, "ReportComputed.Month": {"NotFilterable": true}, "ReportComputed.Quarter": {"NotFilterable": true}, "ReportComputed.Year": {"NotFilterable": true}, "ReportComputed.DayOfWeek": {"NotFilterable": true}, "Report.AppName": {}, "Report.AppBundle": {}, "Report.AppStoreUrl": {}, "Report.Downloads": {}, "Report.PostClickDownloadRate": {}, "Report.CostPerDownload": {}, "Report.AppInstalls": {}, "Report.PostClickInstallRate": {}, "Report.CPI": {}, "Report.Purchases": {}, "Report.PostInstallPurchaseRate": {}, "Report.CPP": {}, "Report.Subscriptions": {}, "Report.PostInstallSubscriptionRate": {}, "Report.CPS": {}}, "NoMergeColumnSetsForAnAccount": ["Report.AdGroupId"], "NoMergeColumnSetsForMultipleAccounts": ["Report.AdGroupId", "Report.AccountId", "ReportComputed.AccountNumber"], "ProcName": "dbo.Uni_ContentPerformanceSummary", "DataSource": "Sql", "DbKey": "AdvbiAdgroupDVS", "RequiredColumns": [], "MandateColumnSet": [["Report.PublisherUrl", "Report.Content", "Report.ContentType"]], "ReportGroup": "ContentPerformanceReport", "ScopeFilterLevel": "Account,Campaign", "AuthorizeOperation": "BIReportPublisherPlacementPerformanceLoad", "VersionNumber": 4, "SortingMethod": "StandardWithGoal", "AggregationExclusion": {}, "DataRetention": {"Hourly": "2m", "Daily": "36m"}, "InlineChartDimensionColumn": "AccountId", "CollapseRowsCondition": {"Report.Spend": 0}}