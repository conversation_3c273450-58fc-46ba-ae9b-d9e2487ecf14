﻿namespace Microsoft.Advertising.Advertiser.Api.V2.ReportPreview
{
    using System;
    using System.Collections.Generic;
    using System.Configuration;
    using System.Diagnostics;
    using CampaignMiddleTierTest.Framework;
    using CampaignMiddleTierTest.Framework.Clickhouse;
    using CampaignMiddleTierTest.Framework.Configuration;
    using CampaignMiddleTierTest.Framework.CustomerCreation;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.Aggregator;
    using Microsoft.AdCenter.Shared.MT;
    using Microsoft.Advertising.Advertiser.Api.V2.ReportDownload;
    using Microsoft.Advertising.Advertiser.MT.AssetGroup;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using TimeZone = AdCenter.Advertiser.CampaignManagement.MT.Entities.TimeZone;

    [TestClass]
    public class ReportPreviewTests : CampaignTestBase
    {
        private CustomerInfo customerInfo;

        private bool SkipTestInSI()
        {
            if (ConfigurationManager.AppSettings.GetValues("AdCenterEnvironment")[0].ToUpper().Contains("SI") )
            {
                Console.WriteLine($"Skipping test because environment is SI or DefaultCustomerPilot is {Features.DatamartClickhouseMigrationPhase2}.");
                return true;
            }
            return false;
        }
        private CustomerInfo multiAccountCustomer;
        private TestCampaignCollection campaignCollection;
        private List<TestAdGroupCollection> adGroupCollection;
        private List<TestKeywordCollection> keywordCollection;
        private List<TestAdCollection> adCollection;

        private string UNIFIED_PREVIEW_SCHEMA_NAME = "UIUnifiedPreview";


        private int numOfCampaigns = 2;
        private int numOfAdGroups = 2;
        private int numOfKeywordsPerAdGroup = 3;

        [TestInitialize]
        public void Initialize()
        {
            this.customerInfo = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                1,
                false,
                new []{ Features.WhitelistEXTA, Features.PerformanceMaxCampaigns });

            this.multiAccountCustomer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                5,
                false,
                Features.WhitelistEXTA);
        }

        #region SortingAndFiltering
        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedQuery_DimensionColumnFilteringSorting(string schema)
        {
            var expectRowCount = 0;
            long keywordId = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 1;
                keywordId = this.keywordCollection[0].Keywords[0].Data.Id;
            }

            dynamic keywordIdFilter = PrepareFilterWithOperator("KeywordId", keywordId.ToString(), "Equals");
            dynamic filter = new[] { new { Filter = keywordIdFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate",
                    "DeliveredMatchType"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("KeywordId", "ASCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 1;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start + i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "1"), ("Ctr", "300.00%"), ("ConversionRate", "66.67%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        [Priority(2)]
        public void ReportPreview_DistributedQuery_DimensionColumnFilteringSorting_DeliverdMatchType(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
            }

            dynamic keywordIdFilter = PrepareFilterWithOperator("DeliveredMatchType", "Exact", "Equals");
            dynamic filter = new[] { new { Filter = keywordIdFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("KeywordId", "ASCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 1;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start + i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "1"), ("Ctr", "300.00%"), ("ConversionRate", "66.67%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        [Priority(2)]
        public void ReportPreview_AccountInfoFiltering_Unselected(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateAccount: true);
                expectRowCount = 10;
            }

            var keywordIdFilter = PrepareFilterWithOperator("AccountNumber", "abcdefg ", "Equals");
            var filter = new[] { new { Filter = keywordIdFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "Clicks"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("KeywordId", "ASCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_CampaignInfoFiltering_Unselected(string schema)
        {
            var expectRowCount = 0;
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 10;
            }

               dynamic keywordIdFilter = PrepareFilterWithOperator("CampaignName", "Fake Campaign", "Equals");
               dynamic filter = new[] { new { Filter = keywordIdFilter } };

               var result = ReportPreviewTestHelper.ExecuteReportPreview(
                   true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                   new[]
                   {
                       "KeywordId",
                       "Impressions",
                   }, 1033,
                   new Tuple<string, string>[]
                   {
                       Tuple.Create("KeywordId", "ASCENDING"),
                   }, null, null, customerInfo, null,
                   null, customerInfo.AccountIds.ToArray(), schemaName : schema);

               Assert.IsNotNull(result);
               Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_DimensionColumnFilteringSorting(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, useRandomSearchTerm: true);
                expectRowCount = 4;
            }

            dynamic searchTermFilter = PrepareFilterWithOperator("SearchQueries", "SearchTerm0", "Equals");
            dynamic filter = new[] { new { Filter = searchTermFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("KeywordId", "ASCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 10;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start + i * 3);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "58"), ("Ctr", "237.93%"), ("ConversionRate", "71.01%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_InvisibleCharacters()
        {
            var expectRowCount = 0;
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, searchTerms: ["豆包", "​​豆包"]);
                expectRowCount = 2;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "SearchQuery",
                    "Impressions",
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);        
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_LocalQuery_SortFilterByNCharTypeColumn(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 2;
            }

            var reportColumns = new[]
            {
                "AccountNumber",
                "AccountName",
                "CurrencyCode",
                "AdDistribution",
                "Impressions",
                "TopImpressionRatePercent",
                "ImpressionSharePercent",
                "AbsoluteTopImpressionSharePercent",
            };

            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("CurrencyCode", "USD", "Equals") } };
            var reportSort = new Tuple<string, string>[] { Tuple.Create("CurrencyCode", "DESCENDING"), };

            // The timestamp of test data are yesterday, today and tomorrow (UTC), so set start date and 
            // end date range large enough to cover all test data. otherwise it might be affected by timezone
            // coverting and daytime saving changes.
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3);

            // 1. Only do filtering
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, null, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            AssertSummaryRow(result, expectRowCount, new[] { ("Impressions", "24"), ("ImpressionSharePercent", "3%") });

            // 2. Only do sorting
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, reportSort, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            AssertSummaryRow(result, expectRowCount, new[] { ("Impressions", "24"), ("ImpressionSharePercent", "3%") });

            // 3. Do both filtering and sorting
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, reportSort, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            AssertSummaryRow(result, expectRowCount, new[] { ("Impressions", "24"), ("ImpressionSharePercent", "3%") });
        }

        // Filter and/or sort by the computed BI column with AllowDBNull = true
        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_LocalQuery_SortFilterByNullableComputedBIColumn(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 1;
            }

            var reportColumns = new[]
            {
                "AccountNumber",
                "AccountName",
                "AdDistribution",
                "Impressions",
                "TopImpressionRatePercent",
                "ImpressionSharePercent",
                "AbsoluteTopImpressionSharePercent",
            };

            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("ImpressionSharePercent", "1", "GreaterThan") } };
            var reportSort = new Tuple<string, string>[] { Tuple.Create("AbsoluteTopImpressionSharePercent", "DESCENDING"), };

            // The timestamp of test data are yesterday, today and tomorrow (UTC), so set start date and 
            // end date range large enough to cover all test data. otherwise it might be affected by timezone
            // coverting and daytime saving changes.
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3); 

            // 1. Only do filtering
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, null, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            AssertSummaryRow(result, expectRowCount, new[] { ("Impressions", "12"), ("ImpressionSharePercent", "3%") });

            // 2. Only do sorting
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                expectRowCount = 2;
            }

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, reportSort, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", expectedValue: "12");
            }

            AssertSummaryRow(result, expectRowCount, new[] { ("Impressions", "24"), ("ImpressionSharePercent", "3%") });

            // 3. Do both filtering and sorting
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                expectRowCount = 1;
            }

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, reportSort, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", expectedValue: "12");
            }

            AssertSummaryRow(result, expectRowCount, new[] { ("Impressions", "12"), ("ImpressionSharePercent", "3%") });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_LocalQuery_SortFilterByNullableByteColumn(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
            }

            var reportColumns = new[]
            {
                "AccountNumber",
                "AccountName",
                "CurrencyCode",
                "GoalType",
                "AdDistribution",
                "Impressions",
                "Clicks",
                "CTR",
            };

            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("GoalType", "3", "Equals") } };
            var reportSort = new Tuple<string, string>[] { Tuple.Create("GoalType", "DESCENDING"), };

            // The timestamp of test data are yesterday, today and tomorrow (UTC), so set start date and 
            // end date range large enough to cover all test data. otherwise it might be affected by timezone
            // coverting and daytime saving changes.
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3);

            // 1. Only do filtering
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, null, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 2. Only do sorting
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                expectRowCount = 2;
            }
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, reportSort, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 3. Do both filtering and sorting
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                expectRowCount = 0;
            }
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                reportColumns, 1033, reportSort, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        [Priority(2)]
        public void ReportPreview_DistributedQuery_GoalColumns_Validation(string schema)
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);            }

            //Call #1: Goal
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "CTR",
                    "Goal",
                    "ConversionRateQualified",
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);

            //Call #2: GoalType
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "CTR",
                    "GoalType",
                    "ConversionRateQualified",
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);

            //Call #3: GoalCategory
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "CTR",
                    "GoalCategory",
                    "ConversionRateQualified",
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_SortFilterByNullableStringColumn(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now);
            }

            var reportColumns = new[]
            {
                "Keyword",
                "KeywordId",
                "AdGroupId",
                "CampaignId",
                "SearchQuery",
                "Impressions",
                "Spend",
                "CTR",
                "ConversionRate"
            };

            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("Keywords", "searchkeywords", "Equals") } };
            var reportSort = new Tuple<string, string>[] { Tuple.Create("Keyword", "DESCENDING"), };

            // 1. Only do filtering
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                reportColumns, 1033,
                null, filter, null, customerInfo, null, null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 2. Only do sorting
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                expectRowCount = 10;
            }
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                reportColumns, 1033,
                reportSort, null, null, customerInfo, null, null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 3. Do both filtering and sorting
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                expectRowCount = 0;
            }
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                reportColumns, 1033,
                reportSort, filter, null, customerInfo, null, null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedQuery_ComputedBIColumnFilteringSorting(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 5;
            }

            dynamic ctrFilter = PrepareFilterWithOperator("CTR", "135", "GreaterThan");
            dynamic filter = new[] { new { Filter = ctrFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "CTR",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("CTR", "DESCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 1;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start + i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "15"), ("CTR", "166.67%"), ("ConversionRate", "80.00%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        
        public void ReportPreview_DistributedAggregationQuery_ComputedBIColumnFilteringSorting(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now);
                expectRowCount = 5;
            }

            dynamic ctrFilter = PrepareFilterWithOperator("CTR", "240", "GreaterThan");
            dynamic filter = new[] { new { Filter = ctrFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "CTR",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("CTR", "DESCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 10;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start + i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "60"), ("CTR", "266.67%"), ("ConversionRate", "68.75%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        
        public void ReportPreview_DistributedQuery_NoFiltering_SortByComputedBIColumn(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 10;
            }

            var reportSort = new Tuple<string, string>[] { Tuple.Create("CTR", "DESCENDING") };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "CTR",
                }, 1033,
                reportSort, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 1;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start + i);
            }

            AssertSummaryRow(result, expectRowCount, new[] { ("Impressions", "55"), ("CTR", "136.36%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_NoFiltering_SortByComputedBIColumn(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now);
                expectRowCount = 10;
            }

            var reportSort = new Tuple<string, string>[] { Tuple.Create("CTR", "DESCENDING") };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "CTR",
                }, 1033,
                reportSort, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 10;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start + i);
            }

            AssertSummaryRow(result, expectRowCount, new[] { ("Impressions", "145"), ("CTR", "237.93%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedQuery_DecimalBIColumnFilteringSorting(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 5;
            }

            dynamic spendFilter = PrepareFilterWithOperator("Spend", "5.0", "LessThan");
            dynamic filter = new[] { new { Filter = spendFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Spend", "DESCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 1 + expectRowCount - 1;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "15"), ("Ctr", "166.67%"), ("ConversionRate", "80.00%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_DecimalBIColumnFilteringSorting(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now);
                expectRowCount = 5;
            }

            dynamic spendFilter = PrepareFilterWithOperator("Spend", "5.0", "LessThan");
            dynamic filter = new[] { new { Filter = spendFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Spend", "DESCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 10 + expectRowCount - 1;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "60"), ("Ctr", "266.67%"), ("ConversionRate", "68.75%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedQuery_FilterOnSpend_2DecimalRounding(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 1;
            }

            // Filter on spend column. In DB it is decimal and returns with several digits after decimal. Test if filter with 2 decimal places works
            dynamic spendFilter = PrepareFilterWithOperator("Spend", "3.80", "Equals");
            dynamic filter = new[] { new { Filter = spendFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("KeywordId", "ASCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 4;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start + i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "4"), ("Ctr", "150.00%"), ("ConversionRate", "83.33%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedQuery_NoSorting_FilterByUnselectedBIColumn(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 5;
            }

            dynamic conversionsFilter = PrepareFilterWithOperator("Conversions", "7", "LessThan");
            dynamic filter = new[] { new { Filter = conversionsFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "Ctr"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Spend", "DESCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 1 + expectRowCount - 1;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "15"), ("Ctr", "166.67%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_NoSorting_FilterByUnselectedBIColumn(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now);
                expectRowCount = 5;
            }

            dynamic conversionsFilter = PrepareFilterWithOperator("Conversions", "25", "LessThan");
            dynamic filter = new[] { new { Filter = conversionsFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "Ctr",
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Spend", "DESCENDING"),
                }, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 10 + expectRowCount - 1;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "60"), ("Ctr", "266.67%") });
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_LocalQuery_NoSorting_FilterByUnselectedComputedBIColumn(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 2;
            }

            // Scenario 1: filter by 'CTR > 50%'
            dynamic ctrFilter = PrepareFilterWithOperator("CTR", "50", "GreaterThan");
            dynamic filter = new[] { new { Filter = ctrFilter } };
            
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3); 
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                }, 1033, null, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // Scenario 2: filter by 'CTR < 50%'
            expectRowCount = 0;
            ctrFilter = PrepareFilterWithOperator("CTR", "50", "LessThan");
            filter = new[] { new { Filter = ctrFilter } };

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                }, 1033, null, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedQuery_NoSorting_FilterByUnselectedComputedBIColumn(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 5;
            }

            dynamic ctrFilter = PrepareFilterWithOperator("CTR", "135", "GreaterThan");
            dynamic filter = new[] { new { Filter = ctrFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "ConversionRate"
                }, 1033,
                null, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            AssertSummaryRow(result, expectRowCount, 
                new[] { ("Impressions", "15"), ("ConversionRate", "80.00%") }, new[] { "CTR" });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_NoSorting_FilterByUnselectedComputedBIColumn(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now);
                expectRowCount = 5;
            }

            dynamic ctrFilter = PrepareFilterWithOperator("CTR", "240", "GreaterThan");
            dynamic filter = new[] { new { Filter = ctrFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "ConversionRate"
                }, 1033,
                null, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            AssertSummaryRow(result, expectRowCount, 
                new[] { ("Impressions", "60"), ("ConversionRate", "68.75%") }, new[] { "CTR" });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_AdPerformance_AdStrengthFilter_CIOnly(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 1, 10);
                ReportPreviewTestHelper.InitializeAdMockPerfData(
                    this.adGroupCollection, this.adCollection, this.customerInfo, DateTime.Now.AddDays(-3), DateTime.Now.AddDays(-2));
                // Mock ad strength data
                DatabaseHelper.InsertAdDataPropertyToBIDB(
                    this.customerInfo, this.customerInfo.AccountIds[0], this.adCollection[0].AdGroup.Data.Id, this.adCollection[0].Ads[0].Data.Id,
                    63, "{\"a\": 1,\"ai\":[2,4,5,8]}");
                expectRowCount = 1;
            }

            // Test filter equals
            dynamic columns = new[]
                {
                    "AdId",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Spend",
                    "ConversionRate",
                    "AdStrength",
                    "AdStrengthActionItems"
                };
            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("AdStrength", "Poor", "Equals") } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "AdPerformanceReport", columns, 1028, null, filter, null, customerInfo, null, null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // Test filter does not equal
            filter = new[] { new { Filter = PrepareFilterWithOperator("AdStrength", "Poor", "DoesNotEqual") } };

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "AdPerformanceReport", columns, 1033, null, filter, null, customerInfo, null, null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.AreEqual(0, result["@odata.count"].Value);
        }

        #endregion SortingAndFiltering

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_SupportEmptyReportScope(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 2;
            }

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3); 
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                    "TopImpressionRatePercent",
                    "ImpressionSharePercent",
                    "AbsoluteTopImpressionSharePercent",
                }, 1033, null, null, null, customerInfo, schemaName: schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [Ignore] //TODO: Need to special handling for those mocked accounts
        public void ReportPreview_NoAccount()
        {
            // Customer with no Accounts
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
                CustomerFactory.TargetCountry.US,
                CustomerFactory.TargetLanguage.English,
                0,
                false,
                Features.WhitelistEXTA);

            var expectRowCount = 0;

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3);
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                    "TopImpressionRatePercent",
                    "ImpressionSharePercent",
                    "AbsoluteTopImpressionSharePercent",
                }, 1033, null, null, null, customer, customerScopes:new long[] {customer.CustomerId}, expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest);

            Assert.IsNotNull(result);
            Assert.AreEqual("CustomerDoesNotHaveAnyAccounts", result["value"][0]["Code"].ToString());
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedQuery_Pagination(string schema)
        {
            var expectRowCount = 0;
            var expectPagedRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 10;
                expectPagedRowCount = 5;
            }

            // First Page
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Ctr",
                    "ConversionRate",
                    "Spend",
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Spend", "DESCENDING"),
                }, null, Tuple.Create(1, 5), customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null, null, true, schemaName : schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 1 + expectRowCount - 1;
            for (int i = 0; i < expectPagedRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i);
            }

            AssertSummaryRow(result, expectPagedRowCount,
                new[] { ("Impressions", "55"), ("Ctr", "136.36%"), ("ConversionRate", "86.67%") });

            // Second Page should use paging cookie and hit cache
            result = ReportPreviewTestHelper.ExecuteReportPreview(
               true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
               new[]
               {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Ctr",
                    "ConversionRate",
                    "Spend"
               }, 1033,
               new Tuple<string, string>[]
               {
                    Tuple.Create("Spend", "DESCENDING"),
               }, null, Tuple.Create(2, 5), customerInfo, null,
               null, customerInfo.AccountIds.ToArray(), null, null, null, true, schemaName : schema
           );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectPagedRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i - expectPagedRowCount);
            }

            AssertSummaryRow(result, expectPagedRowCount,
                new[] { ("Impressions", "55"), ("Ctr", "136.36%"), ("ConversionRate", "86.67%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedAggregationQuery_ImpressionsFormatting(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportDataDownloadTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, useLargeData: true);
                expectRowCount = 10;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("KeywordId", "ASCENDING"),
                }, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "10,045"), ("ConversionRate", "66.72%"), ("Ctr", "299.10%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_Pagination(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            var expectPagedRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now);
                expectRowCount = 10;
                expectPagedRowCount = 5;
            }

            // First Page
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Clicks",
                    "Conversions",
                    "Ctr",
                    "Spend"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Spend", "DESCENDING"),
                }, null, Tuple.Create(1, 5), customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null, null, true
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 10 + expectRowCount - 1;
            for (int i = 0; i < expectPagedRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i);
            }

            AssertSummaryRow(result, expectPagedRowCount,
                new[] { ("Impressions", "145"), ("Clicks", "345"), ("Conversions", "245"), ("Ctr", "237.93%") });

            // Second Page should use paging cookie and hit cache
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Clicks",
                    "Conversions",
                    "Ctr",
                    "Spend"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Spend", "DESCENDING"),
                }, null, Tuple.Create(2, 5), customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null, null, true
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectPagedRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i - expectPagedRowCount);
            }

            AssertSummaryRow(result, expectPagedRowCount,
                new[] { ("Impressions", "145"), ("Clicks", "345"), ("Conversions", "245"), ("Ctr", "237.93%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_DistributedQuery_FastforwardPaging(string schema)
        {
            var expectRowCount = 0;
            var expectPagedRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 10;
                expectPagedRowCount = 5;
            }

            int start = 1 + expectRowCount - 1;

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
               true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
               new[]
               {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Ctr",
                    "ConversionRate",
                    "Spend",
               }, 1033,
               new Tuple<string, string>[]
               {
                    Tuple.Create("Spend", "DESCENDING"),
               }, null, Tuple.Create(2, 5), customerInfo, null,
               null, customerInfo.AccountIds.ToArray(), null, null, schemaName : schema
           );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectPagedRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i - expectPagedRowCount);
            }

            AssertSummaryRow(result, expectPagedRowCount,
                new[] { ("Impressions", "55"), ("Ctr", "136.36%"), ("ConversionRate", "86.67%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_FastforwardPaging(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            var expectPagedRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now);
                expectRowCount = 10;
                expectPagedRowCount = 5;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Clicks",
                    "Conversions",
                    "Ctr",
                    "Spend"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Spend", "DESCENDING"),
                }, null, Tuple.Create(2, 5), customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            int start = 10 + expectRowCount - 1;
            for (int i = 0; i < expectPagedRowCount; i++)
            {
                AssertBIRowColumn(result, i, "Impressions", start - i - expectPagedRowCount);
            }

            AssertSummaryRow(result, expectPagedRowCount,
                new[] { ("Impressions", "145"), ("Clicks", "345"), ("Conversions", "245"), ("Ctr", "237.93%") });
        }

        #region Cache
        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_RequestedColumnsOrderChanged_ShouldNotHitCache(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
                expectRowCount = 10;
            }

            // 1. Make a preview call which will put its result into cache
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastThirtyDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "GregorianDate",
                    "CampaignName",
                    "Revenue"
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), useCache: true, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 2. Make the 2nd call, the only difference is the 2nd call has a different ordering for request columns, it should not hit the cache created in #1
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastThirtyDays, "KeywordPerformanceReport",
                new[]
                {
                    "GregorianDate",
                    "CampaignName",
                    "Revenue",
                    "KeywordId"
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), useCache: true, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_FilterByUnselectedColumn_ShouldNotHitCache(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 2;
            }

            // 1. Make a preview call which will put its result into cache
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3); 
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, useCache: true, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 2. Make the 2nd call, the only difference is the 2nd call has an unselected filter, it should not hit the cache created in #1
            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("AccountStatus", "Active", "Equals") } };
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                }, 1033, null, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, useCache: true, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 3. Make the same call to #2 call again, it should hit cache created in #2
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                }, 1033, null, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, useCache: true, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_FilterBySelectedColumn_ShouldHitCache(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 2;
            }

            // 1. Make a preview call which will put its result into cache
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3); 
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AccountStatus",
                    "AdDistribution",
                    "Impressions",
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, useCache: true, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 2. Make the 2nd call, the only difference with #1 call is the 2nd call has an selected filter, it should hit the cache created in #1
            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("AccountStatus", "Active", "Equals") } };
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AccountStatus",
                    "AdDistribution",
                    "Impressions",
                }, 1033, null, filter, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, useCache: true, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            // 3. Make the 3rd by removing the filter, it should still hit the cache
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AccountStatus",
                    "AdDistribution",
                    "Impressions",
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, useCache: true, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_DifferentColumns_ShouldNotHitCache(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 10);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateAccount: true, populateCampaign: false);
                expectRowCount = 10;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "AccountName", 
                    "AdGroupId", 
                    "SearchQuery", 
                    "Clicks",
                    "Impressions"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null, null, true
            );

            Assert.IsNotNull(result);

            // Should not hit cache since columns are different
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "AccountName", 
                    "AdGroupId", 
                    "SearchQuery", 
                    "Clicks", 
                    "Impressions",
                    "AccountId",
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null, null, true
            );


            // Should not hit cache since column sare different
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "AccountName",
                    "AdGroupId",
                    "SearchQuery",
                    "Clicks",
                    "Impressions",
                    "AccountId",
                    "Ctr"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null, null, true
            );

            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_CampignScope_ShouldHitCache(int[] pilotFeatures)
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            SetUpCollectionData(this.customerInfo, 1, 1, 10);
            ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateAccount: true, populateCampaign: false);
            var expectRowCount = 1;

            var campaignId = this.campaignCollection.Campaigns[0].Data.Id;
            var campaignScope = new Tuple<long, long>[] { new Tuple<long, long>(this.customerInfo.AccountIds[0], campaignId) };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "AccountName",
                    "AdGroupId",
                    "SearchQuery",
                    "Clicks",
                    "Impressions"
                }, 1033,
                null, null, null, customerInfo, null,
                null, null, campaignScope, null, null, true
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "AccountName",
                    "AdGroupId",
                    "SearchQuery",
                    "Clicks",
                    "Impressions"
                }, 1033,
                null, null, null, customerInfo, null,
                null, null, campaignScope, null, null, true
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }
        #endregion Cache

        #region Aggregation

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_AggregateAtAdGroupLevel(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 2, 6);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, useRandomSearchTerm: true);
                expectRowCount = 6;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "AccountId",
                    "AdGroupId",
                    "CampaignId",
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Impressions", "ASCENDING"),
                }, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectRowCount; i++)
            {
                var impressionStart = i < 3 ? 23 : 29;

                AssertDimensionRowColumn(result, i, "SearchQuery", "SearchTerm" + i % 3);
                AssertBIRowColumn(result, i, "Impressions", impressionStart + i * 2);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "186"), ("Ctr", "229.03%"), ("ConversionRate", "71.83%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_AggregateAtAccountLevel(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 2, 6);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, useRandomSearchTerm: true);
                expectRowCount = 3;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "AccountId",
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Impressions", "ASCENDING"),
                }, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            var impressionStart = 58;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertDimensionRowColumn(result, i, "SearchQuery", "SearchTerm" + i % 3);
                AssertBIRowColumn(result, i, "Impressions", impressionStart + i * 4);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "186"), ("Ctr", "229.03%"), ("ConversionRate", "71.83%") });
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedAggregationQuery_AggregateAtCustomerLevel(int[] pilotFeatures)
        {
            var expectRowCount = 0;
            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 2, 6);
                ReportPreviewTestHelper.InitializeSearchQueryMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, useRandomSearchTerm: true);
                expectRowCount = 3;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "SearchQueryPerformanceReport",
                new[]
                {
                    "SearchQuery",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                new Tuple<string, string>[]
                {
                    Tuple.Create("Impressions", "ASCENDING"),
                }, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            var impressionStart = 58;
            for (int i = 0; i < expectRowCount; i++)
            {
                AssertDimensionRowColumn(result, i, "SearchQuery", "SearchTerm" + i % 3);
                AssertBIRowColumn(result, i, "Impressions", impressionStart + i * 4);
            }

            AssertSummaryRow(result, expectRowCount,
                new[] { ("Impressions", "186"), ("Ctr", "229.03%"), ("ConversionRate", "71.83%") });
        }

        #endregion

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_PartialConversion(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 6;
            }

            // The timestamp of test data are yesterday, today and tomorrow (UTC), so set start date and 
            // end date range large enough to cover all test data. otherwise it might be affected by timezone
            // coverting and daytime saving changes.
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3);
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "Spend",
                    "Conversions",
                    "ConversionsQualified",
                    "CostPerConversionQualified",
                    "ConversionRateQualified",
                    "ViewThroughCostPerConversionQualified",
                    "ViewThroughConversionRateQualified",
                    "AllConversionsQualified",
                    "AllCostPerConversionQualified",
                    "AllConversionRateQualified",
                    "LowQualityConversionsQualified",
                    "GregorianDate",
                    "DayOfWeek",
                    "HourOfDay",
                    "Month"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_NonNullableTimeColumns(string schema)
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 6;
            }

            // The timestamp of test data are yesterday, today and tomorrow (UTC), so set start date and 
            // end date range large enough to cover all test data. otherwise it might be affected by timezone
            // coverting and daytime saving changes.
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3); 
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "AverageCPC",
                    "Spend",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent",
                    "Conversions",
                    "ConversionRate",
                    "CostPerConversion",
                    "GregorianDate",
                    "DayOfWeek",
                    "HourOfDay",
                    "Month"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_NewCVRL_N_VideoColumns(string schema)
        {
            var expectRowCount = 0;


            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 6;
            }

            // The timestamp of test data are yesterday, today and tomorrow (UTC), so set start date and 
            // end date range large enough to cover all test data. otherwise it might be affected by timezone
            // coverting and daytime saving changes.
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3);
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "AverageCPC",
                    "Spend",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent",
                    "Conversions",
                    "ConversionRate",
                    "CostPerConversion",
                    "GregorianDate",
                    "DayOfWeek",
                    "HourOfDay",
                    "Month",
                    "VideoStarts",
                    "VideoSkips",
                    "CPCV",
                    "RevenueAdjustment",
                    "AllRevenueAdjustment", 
                    "ViewThroughRevenueAdjustment"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_ProductAdsReports_NullableTimeColumns_PartialConversion(string schema)
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            var expectedRowCount = 0;
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 0, 1);
                ReportDataDownloadTestHelper.InitializeProductPartitionReportMockData(customerInfo, campaignCollection, adGroupCollection, adCollection, true, false);
                expectedRowCount = 3;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, DateTime.UtcNow.Date, DateTime.UtcNow.Date, null, "ProductPartitionPerformanceReport",
                new[]
                {
                    "Week",
                    "AccountId",
                    "AccountName",
                    "AccountNumber",
                    "AccountStatus",
                    "CurrencyCode",
                    "AdGroupId",
                    "AdGroupName",
                    "PartitionType",
                    "Impressions",
                    "Revenue",
                    "Conversions",
                    "RevenuePerConversion",
                    "ConversionsQualified",
                    "CostPerConversionQualified",
                    "ConversionRateQualified",
                    "ViewThroughCostPerConversionQualified",
                    "ViewThroughConversionRateQualified",
                    "AllConversionsQualified",
                    "AllCostPerConversionQualified",
                    "AllConversionRateQualified",
                },
                1033, null, null, null, this.customerInfo, null, null, this.customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectedRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_ProductDimensionAdsReports_NullableTimeColumns_PartialConversion(string schema)
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            var expectedRowCount = 0;
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 0, 1);
                ReportDataDownloadTestHelper.InitializeProductPartitionReportMockData(customerInfo, campaignCollection, adGroupCollection, adCollection, true, false);
                expectedRowCount = 0;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, DateTime.UtcNow.Date, DateTime.UtcNow.Date, null, "ProductDimensionPerformanceReport",
                new[]
                {
                    "Year",
                    "AccountId",
                    "AccountName",
                    "AccountNumber",
                    "AccountStatus",
                    "CurrencyCode",
                    "CampaignType",
                    "AdGroupId",
                    "AdGroupName",
                    "AssetGroupId",
                    "AssetGroupName",
                    "MerchantProductId",
                    "Brand",
                    "StoreId",
                    "ProductType1",
                    "ProductCategory1",
                    "Impressions",
                    "Revenue",
                    "Conversions",
                    "RevenuePerConversion",
                    "ReturnOnAdSpendCPS",
                    "RevenueCPS"
                },
                1033, null, null, null, this.customerInfo, null, null, this.customerInfo.AccountIds.ToArray(), schemaName : schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectedRowCount, result["@odata.count"].Value);
        }


        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_UserLocationPerformanceReport(string schema)
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(customerInfo, 1, 1, 1);

                ReportPreviewTestHelper.InitializeLocationReportMockData(customerInfo, this.adGroupCollection);
                expectRowCount = 1;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.AllTime, "UserLocationPerformanceReport",
                new[]
                {
                    "AccountId",
                    "LocationId",
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "AverageCPC",
                    "Spend",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent",
                    "Conversions",
                    "ConversionRate",
                    "CostPerConversion"
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null, null, schemaName : schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_PublisherUsagePerformanceReport_PerformanceMax(string schema)
        {
            if (SkipTestInSI())
            {
                return;
            }
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
               CustomerFactory.TargetCountry.US,
               CustomerFactory.TargetLanguage.English,
               1,
               false,
               Features.PerformanceMaxCampaigns,
               Features.UnifiedAppCampaign);
            AssetGroupTestHelper.CreateGoals(customer);
            ReportDataDownloadTestHelper.InitializePublisherUsageReportMockData(customer, true, true, true);

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                 new string[] { "PublisherUrl", "CampaignId", "CampaignName", "CampaignStatus", "CampaignType", "AdGroupId", "AdGroupName", "AdGroupStatus", "AssetgroupId", "AssetGroupName", "AssetGroupStatus", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "Conversions", "AppInstalls", "Downloads", "Purchases", "Subscriptions" }
                 ,1033, null, null, null, customer, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customer.CustomerId }, null, null, null, schemaName : schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(2, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        public void ReportPreview_ContentPerformance(string schema)
        {
            if (SkipTestInSI())
            {
                return;
            }
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
               CustomerFactory.TargetCountry.US,
               CustomerFactory.TargetLanguage.English,
               1,
               false,
               Features.PerformanceMaxCampaigns,
               Features.UnifiedAppCampaign);
            AssetGroupTestHelper.CreateGoals(customer);
            ReportDataDownloadTestHelper.InitializePublisherUsageReportMockData(customer, true, true, true);

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastSevenDays, "ContentPerformanceReport",
                 new string[] { "PublisherUrl", "CampaignId", "CampaignName", "CampaignStatus", "CampaignType", "AdGroupId", "AdGroupName", "AdGroupStatus", "AssetgroupId", "AssetGroupName", "AssetGroupStatus", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "Conversions", "AppInstalls", "Downloads", "Purchases", "Subscriptions" }
                 , 1033, null, null, null, customer, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customer.CustomerId }, null, null, null, schemaName: schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(2, result["@odata.count"].Value);
        }


        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_PublisherUsagePerformanceReport_AppCampaign(string schema)
        {
            if (SkipTestInSI())
            {
                return;
            }
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
               CustomerFactory.TargetCountry.US,
               CustomerFactory.TargetLanguage.English,
               1,
               false,
               Features.UnifiedAppCampaign);
            AssetGroupTestHelper.CreateGoals(customer);
            ReportDataDownloadTestHelper.InitializePublisherUsageReportMockData(customer, true, true);

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                 new string[] { "PublisherUrl", "CampaignId", "CampaignName", "CampaignStatus", "CampaignType", "AppName", "AppBundle", "AppStoreUrl", "Impressions", "Clicks", "AppInstalls", "Downloads", "Purchases", "Subscriptions" }
                 , 1033, null, null, null, customer, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customer.CustomerId }, null, null, null, schemaName: schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(1, result["@odata.count"].Value);
            var row = result["value"][0];

            Trace.WriteLine((string)row.ToString());
            Assert.AreEqual("100", row["Downloads"]["Value"].ToString());
            Assert.AreEqual("100", row["AppInstalls"]["Value"].ToString());
            Assert.AreEqual("100", row["Subscriptions"]["Value"].ToString());
            Assert.AreEqual("100", row["Downloads"]["Value"].ToString());

            Assert.AreEqual("Fake App", row["AppName"]["Value"].ToString());
            Assert.AreEqual("com.microsoft.exampleapp", row["AppBundle"]["Value"].ToString());
            Assert.AreEqual("play.google.com/store/apps/details?id=com.microsoft.exampleapp", row["AppStoreUrl"]["Value"].ToString());
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_UserLocationPerformanceReport_PerformanceMax(string schema)
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
               CustomerFactory.TargetCountry.US,
               CustomerFactory.TargetLanguage.English,
               1,
               false,
               Features.PerformanceMaxCampaigns);
            AssetGroupTestHelper.CreateGoals(customer);
            ReportDataDownloadTestHelper.InitializeLocationReportMockData(customer, true, true, true);

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastSevenDays, "UserLocationPerformanceReport",
                new string[] { "AccountId", "CampaignName", "CampaignType", "AdGroupId", "AdGroupName", "AssetgroupId", "AssetGroupName", "LocationId", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CostPerConversion", "Conversions" },
                1033, null, null, null, customer, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customer.CustomerId }, null, null, null, schemaName : schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(2, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        [Ignore]
        public void ReportPreview_GeographicPerformanceReport_PerformanceMax(string schema)
        {
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(
               CustomerFactory.TargetCountry.US,
               CustomerFactory.TargetLanguage.English,
               1,
               false,
               Features.PerformanceMaxCampaigns);
            AssetGroupTestHelper.CreateGoals(customer);
            ReportDataDownloadTestHelper.InitializeLocationReportMockData(customer, true, true, true);

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastSevenDays, "GeographicPerformanceReport",
                new string[] { "Country", "AccountId", "CampaignName", "CampaignType", "AdGroupId", "AdGroupName", "AssetgroupId", "AssetGroupName", "AssetGroupStatus", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CostPerConversion", "Conversions" },
                1033, null, null, null, customer, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customer.CustomerId }, null, null, null, schemaName : schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(2, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        [Ignore]
        public void ReportPreview_UserLocationPerformanceReport_AppCampaign(string schema)
        {
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.IsAccountEnableForAppCampaign, Features.UnifiedAppCampaign);
            AssetGroupTestHelper.CreateGoals(customer);
            ReportDataDownloadTestHelper.InitializeAppCampaignLocationReportMockData(customer, true, true, true);

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastSevenDays, "UserLocationPerformanceReport",
                new string[] { "AccountId", "CampaignId", "CampaignType", "AdGroupId", "AdGroupName", "AssetgroupId", "AssetGroupName", "LocationId", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CostPerConversion", "Conversions", "Downloads", "PostClickDownloadRate", "CostPerDownload", "AppInstalls", "PostClickInstallRate", "CPI", "Purchases", "PostInstallPurchaseRate", "CPP", "Subscriptions", "PostInstallSubscriptionRate", "CPS" },
                1033, null, null, null, customer, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customer.CustomerId }, null, null, null, schemaName: schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(2, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        [Ignore]
        public void ReportPreview_GeographicPerformanceReport_AppCampaign(string schema)
        {
            var customer = CustomerInfo.CreateStandardMSAdvertiserWithPilots(CustomerFactory.TargetCountry.US, CustomerFactory.TargetLanguage.English, 1, false, Features.IsAccountEnableForAppCampaign, Features.UnifiedAppCampaign);
            AssetGroupTestHelper.CreateGoals(customer);
            ReportDataDownloadTestHelper.InitializeAppCampaignLocationReportMockData(customer, true, true, true);

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastSevenDays, "GeographicPerformanceReport",
                new string[] { "Country", "AccountId", "CampaignName", "CampaignType", "AdGroupId", "AdGroupName", "AssetgroupId", "AssetGroupName", "AssetGroupStatus", "Impressions", "Clicks", "CTR", "AverageCPC", "AveragePosition", "ConversionRate", "CostPerConversion", "Conversions", "Downloads", "PostClickDownloadRate", "CostPerDownload", "AppInstalls", "PostClickInstallRate", "CPI", "Purchases", "PostInstallPurchaseRate", "CPP", "Subscriptions", "PostInstallSubscriptionRate", "CPS" },
                1033, null, null, null, customer, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customer.CustomerId }, null, null, null, schemaName: schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(2, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [Ignore]
        public void ReportPreview_AccountPerformanceReport_SixWeeks()
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 1;
            }

            var startDate = DateTime.UtcNow.AddDays(-3);
            var endDate = DateTime.UtcNow.AddDays(3); 
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport-SixWeeks",
                new[]
                {   
                    "CustomerName",
                    "AccountName",
                    "AccountNumber",
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "AverageCPC",
                    "Spend",
                    "Conversions",
                    "ConversionRate",
                    "CostPerConversion",
                    "Week"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        public void ReportPreview_BudgetSummaryReport()
        {
            var expectRowCount = 0;
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "BudgetSummaryReport",
                new[]
                {
                    "Date",
                    "AccountName",
                    "AccountId",
                    "AccountNumber",
                    "CampaignName",
                    "CampaignId",
                    "CurrencyCode",
                    "MonthlyBudget",
                    "DailySpend",
                    "MonthToDateSpend",
                    "ProductName",
                    "DailyBudget",
                    "TotalBudget",
                    "BudgetType",
                    "TotalSpend",
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        public void ReportPreview_BillingStatementReport()
        {
            var expectRowCount = 0;
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "BillingStatementReport",
                new[]
                {
                    "Month",
                    "CustomerName",
                    "AccountNumber",
                    "CampaignName",
                    "CurrencyCode",
                    "BillToCountry",
                    "BillToCustomerNumber",
                    "BillToCustomerName",
                    "SoldToCustomerNumber",
                    "DocumentNumber",
                    "DocumentType",
                    "BillingType",
                    "NetAmountDue",
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        public void ReportPreview_CustomerInvoiceReport()
        {
            this.customerInfo.AddUser(User.Entity.Customer, User.Role.InternalAdmin, CustomerInfo.SystemCustomerId);
            this.customerInfo.CustomerId = CustomerInfo.SystemCustomerId;

            var expectRowCount = 0;
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "CustomerInvoiceReport",
                new[]
                {
                    "Month",
                    "CustomerName",
                    "AgencyName",
                    "PricingMethod",
                    "SalesHouse",
                    "SoldToCountry",
                    "SoldToCustomerName",
                    "SoldToCustomerNumber",
                    "CurrencyCode",
                    "BilledAmount",
                    "BilledAmountUSD",
                    "BillToCountry",
                    "BillToCustomerNumber",
                    "BillToCustomerName",
                    "Clicks",
                    "DocumentNumber",
                    "DocumentType",
                    "NetRevenue",
                    "NetRevenueUSD",
                    "NetAmountDue",
                }, 1033, null, null, null, this.customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { this.customerInfo.CustomerId });

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_ColumnExclusionCheck()
        {
            // 1. there are excluded columns in requested columns: BudgetAssociationStatus and AbsoluteTopImpressionSharePercent
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "CampaignPerformanceReport",
                new[]
                {
                    "AccountId",
                    "AccountName",
                    "AccountNumber",
                    "CampaignId",
                    "BudgetAssociationStatus",
                    "Spend",
                    "Impressions",
                    "Clicks",
                    "Ctr",
                    "Conversions",
                    "AbsoluteTopImpressionSharePercent"
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest);
            Assert.AreEqual("ExclusiveColumnsSelected", result["value"][0]["Code"].ToString(), "error code");

            // 2. there are excluded columns in filter columns and requested columns: BudgetAssociationStatus and AbsoluteTopImpressionSharePercent
            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("BudgetAssociationStatus", "0", "Equals") } };
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "CampaignPerformanceReport",
                new[]
                {
                    "AccountId",
                    "AccountName",
                    "AccountNumber",
                    "CampaignId",
                    "Spend",
                    "Impressions",
                    "Clicks",
                    "Ctr",
                    "Conversions",
                    "AbsoluteTopImpressionSharePercent"
                }, 1033, null, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest);
            Assert.AreEqual("ExclusiveColumnsSelected", result["value"][0]["Code"].ToString(), "error code");

            // 3. Check filter column alias scenario 
            filter = new[] { new { Filter = PrepareFilterWithOperator("LanguageCode", "Arabic", "Equals") } };
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "AdGroupPerformanceReport",
                new[]
                {
                    "AdGroupName",
                    "Impressions",
                    "AdGroupTypePreview",
                    "PlacementName"
                }, 1033, null, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_RequiredColumnsCheck()
        {
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "ConversionPerformanceReport",
                new[]
                {
                    "GregorianDate",
                    "Spend",
                    "Conversions"
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest);
            Assert.AreEqual("InvalidColumns", result["value"][0]["Code"].ToString(), "error code");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_MandateColumnsCheck()
        {
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "LabelAdGroupReport",
                new[]
                {
                    "GregorianDate",
                    "Spend",
                    "Conversions"
                }, 1033, null, null, null, customerInfo, null, 
                null, customerInfo.AccountIds.ToArray(), expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest, schemaName: UNIFIED_PREVIEW_SCHEMA_NAME);

            Assert.AreEqual("InvalidReportParameters", result["value"][0]["Code"].ToString(), "error code");
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_FeedItemPerformanceReport()
        {
            SetUpCollectionData(this.customerInfo, 1, 1, 1, 1);
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeFeedItemReportMockPerfData(customerInfo, this.campaignCollection, this.adGroupCollection, this.adCollection, DateTime.UtcNow, DateTime.UtcNow);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
            false, null, null, DateRangePreset.AllTime, "FeedItemPerformanceReport",
            new[]
            {
                "FeedId",
                "FeedItemId",
                "AdGroupId",
                "Clicks",
                "Impressions",
                "Conversions",
                "AccountId",
                "AccountName",
                "CampaignName"
            }, 1033, null, null, null, customerInfo, null,
            null, customerInfo.AccountIds.ToArray());
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_ConversionModelCompareReport()
        {
            SetUpCollectionData(this.customerInfo, 1, 1, 1, 1);

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
            false, null, null, DateRangePreset.AllTime, "ConversionModelCompareReport",
            new[]
            {
                "Clicks",
                "Impressions",
                "AccountId",
                "AccountName",
                "CampaignName"
            }, 1033, null, null, null, customerInfo, null,
            null, customerInfo.AccountIds.ToArray());
        }



        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_HotelDimensionPerformanceReport()
        {
            SetUpCollectionData(this.customerInfo, 1, 1, 1, 1);
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeHotelDimensionReportMockPerfData(customerInfo, this.campaignCollection, this.adGroupCollection, this.adCollection, DateTime.UtcNow, DateTime.UtcNow);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
            false, null, null, DateRangePreset.AllTime, "HotelDimensionPerformanceReport",
            new[]
            {
                "AdvertiserHotelId",
                "CampaignHotelId",
                "CampaignId",
                "AdGroupId",
                "Clicks",
                "CountryName",
                "City",
                "Impressions",
                "Conversions"
            }, 1033, null, null, null, customerInfo, null,
            null, customerInfo.AccountIds.ToArray());
            Assert.IsNotNull(result);            
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_HotelGroupPerformanceReport()
        {
            SetUpCollectionData(this.customerInfo, 1, 1, 1, 1);
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeHotelGroupReportMockPerfData(customerInfo, this.campaignCollection, this.adGroupCollection, this.adCollection, DateTime.UtcNow, DateTime.UtcNow);
                //expectRowCount = 1;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
            false, null, null, DateRangePreset.AllTime, "HotelGroupPerformanceReport",
            new[]
            {
                "HotelGroupNodeId",
                "PartitionType",
                "CampaignId",
                "AdGroupId",
                "Clicks",
                "Impressions",
                "Conversions"
            }, 1033, null, null, null, customerInfo, null,
            null, customerInfo.AccountIds.ToArray());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_LocalQuery_Localization_Japanese(int[] pilotFeatures)
        {
            var expectRowCount = 0;

            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, DateTime.UtcNow, DateTime.UtcNow);
                expectRowCount = 1;
            }

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date.AddDays(3); 
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountId",
                    "AccountName",
                    "AccountNumber",
                    "AccountStatus",
                    "CurrencyCode",
                    "Spend",
                    "Impressions",
                    "Clicks",
                    "Ctr",
                    "Conversions"
                }, 1041, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectRowCount; i++)
            {
                AssertDimensionRowColumn(result, i, "AccountStatus", "アクティブ");
            }
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_DistributedQuery_Localization_Japanese(int[] pilotFeatures)
        {
            var expectRowCount = 0;

            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 5);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateAccount: true);
                expectRowCount = 5;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "KeywordPerformanceReport",
                new[]
                {
                    "AccountId",
                    "AccountName",
                    "AccountNumber",
                    "AccountStatus",
                    "CurrencyCode",
                    "CustomerId",
                    "CustomerName",
                    "AdGroupId",
                    "AdGroupName",
                    "Impressions",
                    "Keyword",
                    "KeywordId",
                    "Revenue",
                    "Conversions",
                    "RevenuePerConversion",
                    "Spend",
                }, 1041, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null, null
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectRowCount; i++)
            {
                AssertDimensionRowColumn(result, i, "AccountStatus", "アクティブ");
            }
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_NegativeKeywordConflictReport_DBReturnsAllColumns()
        {
            var expectRowCount = 0;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 5);
                ReportPreviewTestHelper.InitializeNegativeKeywordConflictReportMockData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, populateAccount: true);
                expectRowCount = 5;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "NegativeKeywordConflictReport",
                new[]
                {
                    "AccountId",
                    "AccountName",
                    "AccountNumber",
                    "Keyword",
                    "KeywordId",
                    "NegativeKeyword",
                    "ConflictLevel",
                    "BidMatchType",
                    "NegativeKeywordListId",
                    "NegativeKeywordList",
                    "NegativeKeywordId",
                    "NegativeKeywordMatchType",
                    "ConflictType",
                }, 1033, null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(), null, null
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_BIColumnShouldNotMapToSameDBColumn(int[] pilotFeatures)
        {
            var expectRowCount = 0;

            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 5);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateAccount: true);
                expectRowCount = 5;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "KeywordPerformanceReport",
                new[]
                {
                    "AccountName",
                    "AccountNumber",
                    "AccountStatus",
                    "CurrencyCode",
                    "CustomerId",
                    "CustomerName",
                    "AdGroupId",
                    "AdGroupName",
                    "Impressions",
                    "Keyword",
                    "KeywordId",
                    "Revenue",
                    "Conversions",
                    "RevenuePerConversion",
                    "Spend",
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null, null
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow(new int[] { })]
        //
        public void ReportPreview_ColumnCalculatorShouldUseReportUIFormat(int[] pilotFeatures)
        {
            var expectRowCount = 0;

            if (pilotFeatures != null)
            {
                DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, pilotFeatures);
            }

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 5);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateAccount: true);
                expectRowCount = 5;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "KeywordPerformanceReport",
                new[]
                {
                    "AccountName",
                    "KeywordId",
                    "Revenue",
                    "Spend",
                    "ReturnOnAdSpend"
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null, null
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            
            if(expectRowCount > 0)
            {
                var revenue = (double)result["value"][0]["Revenue"]["RawValue"];
                var spend = (double)result["value"][0]["Spend"]["RawValue"];
                var roas = (double)result["value"][0]["ReturnOnAdSpend"]["RawValue"];

                Assert.AreEqual(Math.Round((revenue / spend)*100, 2), roas, "ROAS does not match expected value");

            }
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_UserLevelScopeFilter()
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            this.customerInfo.AddUser(User.Entity.Customer, User.Role.InternalAdmin, CustomerInfo.SystemCustomerId);
            this.customerInfo.CustomerId = CustomerInfo.SystemCustomerId;

            var biData = new BiData
            {
                Date = DateTime.Today,
                DateRange = 2,
                NumDaysBackForModificationDate = 2,
                ModifiedByUserId = this.customerInfo.UserId.Value,
                CustomerId = this.customerInfo.CustomerId,
                AccountId = this.customerInfo.AccountIds[0]
            };
            BiDatabaseHelper.SetChangeHistoryMockData(this.customerInfo, biData);

            var expectRowCount = 1;

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "FraudChangeHistoryReport",
                new[]
                {
                    "AccountId",
                    "CustomerId",
                    "CustomerName",
                    "AccountName",
                    "ChangeTotalCount",
                    "UserId"
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                null, null, null, userScopes: new long[] { this.customerInfo.UserId.Value }
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            BiDatabaseHelper.SetChangeHistoryMockData(this.customerInfo, biData, clearExistingData: true, isOnlyClear: true);
        }


        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_UserLevelScopeFilter_NotInternalUser()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "FraudChangeHistoryReport",
                new[]
                {
                    "AccountId",
                    "CustomerId",
                    "CustomerName",
                    "AccountName",
                    "ChangeTotalCount",
                    "UserId"
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                null, null, null, userScopes: new long[] { this.customerInfo.UserId.Value }, expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest
            );

            Assert.IsNotNull(result);
            Assert.AreEqual("InvalidScope", result["value"][0]["Code"].Value);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_ChangeHistorySortAndFilterDependentColumns()
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 1);
                ReportPreviewTestHelper.InitializeSearchCampaignChangeHistoryMockData(this.customerInfo, campaignCollection, adGroupCollection, keywordCollection, 12);
            }

            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("ChangedBy", "AHTestLoginUserName_7000", "Equals")} };
            var reportSort = new Tuple<string, string>[] { Tuple.Create("ChangedBy", "DESCENDING"), };

            // Only Sort
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "ChangedBy"
                }, 1033, reportSort, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);


            // Only Filter
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "ChangedBy"
                }, 1033, null, filter, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);

            // Filter And Sort
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "ChangedBy"
                }, 1033, reportSort, filter, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_ChangeHistorySortAndFilterTool()
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 1);
                ReportPreviewTestHelper.InitializeSearchCampaignChangeHistoryMockData(this.customerInfo, campaignCollection, adGroupCollection, keywordCollection, 12);
            }

            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("ChangedBy", "AHTestLoginUserName_7000", "Equals") } };
            var reportSort = new Tuple<string, string>[] { Tuple.Create("Tool", "DESCENDING"), };

            // Only Sort
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "Tool"
                }, 1033, reportSort, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);


            // Only Filter
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "ChangedBy"
                }, 1033, null, filter, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);

            // Filter And Sort
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "ChangedBy",
                    "Tool"
                }, 1033, reportSort, filter, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [Ignore]
        public void ReportPreview_ChangeHistorySortAndFilterOldAndNewvalue()
        {
            //TODO xichen1 will refactor the test after ChangeHistory phase2 Revamp GA
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 1, 1);
                ReportPreviewTestHelper.InitializeSearchCampaignChangeHistoryMockData(this.customerInfo, campaignCollection, adGroupCollection, keywordCollection, 12);
            }

            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("NewValue", "Broad", "Equals") } };
            var reportSort = new Tuple<string, string>[] { Tuple.Create("OldValue", "DESCENDING"), };

            // Only Sort
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "OldValue",
                    "NewValue",
                    "ChangedBy"
                }, 1033, reportSort, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);


            // Only Filter
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "OldValue",
                    "NewValue",
                    "ChangedBy"
                }, 1033, null, filter, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);

            // Filter ANd Sort
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "SearchCampaignChangeHistoryReport",
                new[]
                {
                    "DateTime",
                    "OldValue",
                    "NewValue",
                    "ChangedBy"
                }, 1033, reportSort, filter, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null
            );
            Assert.IsNotNull(result);
        }


        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_CampaignLevelScope(string schema)
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                return;
            }

            SetUpCollectionData(this.customerInfo, 2, 3, 3);
            ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateAccount: true);
            var expectRowCount = 9;

            var campaignId = this.campaignCollection.Campaigns[0].Data.Id;
            var campaignScope = new Tuple<long, long>[] { new Tuple<long, long>(this.customerInfo.AccountIds[0], campaignId) };
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Clicks",
                    "Spend",
                    "Conversions",
                    "Ctr"
                }, 1033, null, null, null, customerInfo, null,
                null, null, campaignScope, null, schemaName : schema
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
            
            for (int i = 0; i < expectRowCount; i++)
            {
                var actualCampaignId = result["value"][i]["CampaignId"]["Value"].ToString();
                Assert.AreEqual(actualCampaignId.Substring(1, actualCampaignId.Length - 2), campaignId.ToString());
            }
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1", "LabelName")]
        [DataRow("UIUnifiedPreview", "LabelNameAccount")]
        public void ReportPreview_LabelAccountReport(string schema, string labelName)
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                DateTime now = DateTime.Now;
                ReportDataDownloadTestHelper.InitializeAccountReportMockPerfData(this.customerInfo, now, now);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.ThisMonth, "LabelAccountReport",
                new[]
                {                    
                    labelName,
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, null, null, null, schemaName: schema
            );
            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1", "LabelName")]
        [DataRow("UIUnifiedPreview", "LabelNameCampaign")]
        public void ReportPreview_LabelCampaignReport(string schema, string labelName)
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                DateTime now = DateTime.Now;
                SetUpCollectionData(this.customerInfo, this.numOfCampaigns, this.numOfAdGroups, this.numOfKeywordsPerAdGroup);
                ReportDataDownloadTestHelper.InitializeCampaignReportMockPerfData(campaignCollection, this.customerInfo, now, now);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.ThisMonth, "LabelCampaignReport",
                new[]
                {
                    labelName,
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, null, null, null, schemaName: schema
            );
            Assert.IsNotNull(result);
        }


        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_LabelCampaignReport_OnlyISMeasure_Fail()
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                DateTime now = DateTime.Now;
                SetUpCollectionData(this.customerInfo, this.numOfCampaigns, this.numOfAdGroups, this.numOfKeywordsPerAdGroup);
                ReportDataDownloadTestHelper.InitializeCampaignReportMockPerfData(campaignCollection, this.customerInfo, now, now);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.ThisMonth, "LabelCampaignReport",
                new[]
                {
                  "LabelName",
                  "ImpressionSharePercent",
                  "AbsoluteTopImpressionSharePercent",
                  "TopImpressionSharePercent"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, null, null, null,
                expectedHttpStatusCode : System.Net.HttpStatusCode.BadRequest
            );
            Assert.IsNotNull(result);
            Assert.AreEqual("RequiredColumnsNotSelected", result["value"][0]["Code"].ToString(), "error code");
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1", "LabelName")]
        [DataRow("UIUnifiedPreview", "LabelNameAdGroup")]
        public void ReportPreview_LabelAdGroupReport(string schema, string labelName)
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                DateTime now = DateTime.Now;
                SetUpCollectionData(this.customerInfo, this.numOfCampaigns, this.numOfAdGroups, this.numOfKeywordsPerAdGroup);
                ReportDataDownloadTestHelper.InitializeAdGroupReportMockPerfData(this.adGroupCollection, this.customerInfo, now, now);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.ThisMonth, "LabelAdGroupReport",
                new[]
                {
                    labelName,
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, null, null, null, schemaName: schema
            );
            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1", "LabelName")]
        [DataRow("UIUnifiedPreview", "LabelNameKeyword")]
        public void ReportPreview_LabelKeywordReport(string schema, string labelName)
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportDataDownloadTestHelper.InitializeKeywordReportMockData(customerInfo);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.ThisMonth, "LabelKeywordReport",
                new[]
                {
                    labelName,
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, null, null, null, schemaName: schema
            );
            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1", "LabelName")]
        [DataRow("UIUnifiedPreview", "LabelNameAd")]
        public void ReportPreview_LabelAdReport(string schema, string labelName)
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                DateTime now = DateTime.Now;
                SetUpCollectionData(this.customerInfo, this.numOfCampaigns, this.numOfAdGroups, this.numOfKeywordsPerAdGroup);
                ReportDataDownloadTestHelper.InitializeAdReportMockPerfData(adGroupCollection, this.customerInfo, now, now);

            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.ThisMonth, "LabelAdReport",
                new[]
                {
                    labelName,
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, null, null, null, schemaName: schema
            );
            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1", "LabelName")]
        [DataRow("UIUnifiedPreview", "LabelNameKeyword")]
        
        public void ReportPreview_LabelKeywordReportUnlabeled(string schema, string labelName)
        {

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 2, 3, 3);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, DateTime.Now.AddDays(-1), DateTime.Now, populateAccount: true);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.ThisMonth, "LabelKeywordReport",
                new[]
                {
                    labelName,
                    "Impressions",
                    "Clicks",
                    "CTR",
                    "TopImpressionRatePercent",
                    "AbsoluteTopImpressionRatePercent"
                }, 1033, null, null, null, customerInfo, null,
                new long[] { customerInfo.CustomerId }, null, null, null, schemaName : schema
            );
            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_InvalidCollapsedRowSelections_BadRequest()
        {
            // Collapsed row with Column value not supported
            dynamic collapsedRowConditionInvalidColumnValue = PrepareFilterWithOperator("Spend", "1", "Equals");
            dynamic collapsedRow = new { ExpandCollapsedRows = true, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumnValue } };
            _ = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest,
                collapsedRow: collapsedRow);

            // Collapsed row with null filter
            collapsedRow = new { ExpandCollapsedRows = true };
            _ = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest,
                collapsedRow: collapsedRow);

            // Collapsed row with Column operator not supported
            dynamic collapsedRowConditionInvalidColumnOperator = PrepareFilterWithOperator("Spend", "0", "Contains");
            collapsedRow = new { ExpandCollapsedRows = true, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumnOperator } };
            _ = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest,
                collapsedRow: collapsedRow);

            // Collapsed row with Column not supported
            dynamic collapsedRowConditionInvalidColumn = PrepareFilterWithOperator("Clicks", "0", "Equals");
            collapsedRow = new { ExpandCollapsedRows = true, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumn } };
            _ = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest,
                collapsedRow: collapsedRow);

            // Collapsed row for report that is not supported
            dynamic validCollapsedRowCondition = PrepareFilterWithOperator("Spend", "0", "Equals");
            collapsedRow = new { ExpandCollapsedRows = true, CollapseCondition = new { Filter = validCollapsedRowCondition } };
            _ = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest,
                collapsedRow: collapsedRow);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_CollapsedRow_Success_CIOnly()
        {
            ReportDataDownloadTestHelper.InitializePublisherUsageReportMockData(customerInfo, false, false, false, adGroupCount: 10);

            // No collapsed rows to get baseline data
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "Spend",
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: null);

            Assert.IsNotNull(result);

            var totalRowCount = int.Parse(result["@odata.count"].ToString());
            var rowsWith0Spend = 0;
            var totalInpressions = 0;
            var totalSummaryRowImpressions = 0;

            foreach(var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var spend = row["Spend"]["Value"].ToString();
                    if (spend == "0.00")
                    {
                        rowsWith0Spend++;
                    }

                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    totalInpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    totalSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Impression count matches between rows and total row
            Assert.AreEqual(totalSummaryRowImpressions, totalInpressions);

            var rowsWithNon0Spend = totalRowCount - rowsWith0Spend;
            dynamic collapsedRowConditionInvalidColumnValue = PrepareFilterWithOperator("Spend", "0", "Equals");

            // Main grid call with collapsed rows not expanded
            dynamic collapsedRow = new { ExpandCollapsedRows = false, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumnValue } };

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "Spend",
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: collapsedRow);

            Assert.IsNotNull(result);

            var notExpandedRowCount = int.Parse(result["@odata.count"].ToString());
            Assert.AreEqual(rowsWithNon0Spend, notExpandedRowCount);

            var notExpandedImpressions = 0;
            var notExpandedSummaryRowImpressions = 0;
            foreach (var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var spend = row["Spend"]["Value"].ToString();
                    if (spend == "0.00")
                    {
                        Assert.Fail("0 Spend data not expected here");
                    }

                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    notExpandedImpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    notExpandedSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Summary row in main grid should include impressions from collapsed rows as well
            Assert.AreEqual(notExpandedSummaryRowImpressions, totalSummaryRowImpressions);
            Assert.IsTrue(notExpandedSummaryRowImpressions > notExpandedImpressions);

            // Collapsed grid call with collapsed rows expanded
            collapsedRow = new { ExpandCollapsedRows = true, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumnValue } };

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "Spend",
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: collapsedRow);

            Assert.IsNotNull(result);

            var expandedRowCount = int.Parse(result["@odata.count"].ToString());
            Assert.AreEqual(rowsWith0Spend, expandedRowCount);

            var expandedImpressions = 0;
            var expandedSummaryRowImpressions = 0;
            foreach (var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var spend = row["Spend"]["Value"].ToString();
                    if (spend != "0.00")
                    {
                        Assert.Fail("non-zero Spend data not expected here");
                    }

                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    expandedImpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    expandedSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Expanded row grid summary rows must match rows from this grid. This total will not include non-zero impressions so this total will be lesser
            Assert.AreEqual(expandedImpressions, expandedSummaryRowImpressions);
            Assert.IsTrue(notExpandedSummaryRowImpressions > expandedSummaryRowImpressions);
            Assert.AreEqual(expandedImpressions + notExpandedImpressions, totalInpressions);

        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_CollapsedRow_CollapsedColumnNotSelected_Success()
        {
            if (SkipTestInSI())
            {
                return;
            }
            ReportDataDownloadTestHelper.InitializePublisherUsageReportMockData(customerInfo, false, false, false, adGroupCount: 10);

            // No collapsed rows to get baseline data
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: null);

            Assert.IsNotNull(result);

            var totalRowCount = int.Parse(result["@odata.count"].ToString());
            var totalInpressions = 0;
            var totalSummaryRowImpressions = 0;

            foreach (var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    totalInpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    totalSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Impression count matches between rows and total row
            Assert.AreEqual(totalSummaryRowImpressions, totalInpressions);

            dynamic collapsedRowConditionInvalidColumnValue = PrepareFilterWithOperator("Spend", "0", "Equals");

            // Main grid call with collapsed rows not expanded
            dynamic collapsedRow = new { ExpandCollapsedRows = false, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumnValue } };

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: collapsedRow);

            Assert.IsNotNull(result);

            var notExpandedRowCount = int.Parse(result["@odata.count"].ToString());

            var notExpandedImpressions = 0;
            var notExpandedSummaryRowImpressions = 0;
            foreach (var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    notExpandedImpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    notExpandedSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Summary row in main grid should include impressions from collapsed rows as well
            Assert.AreEqual(notExpandedSummaryRowImpressions, totalSummaryRowImpressions);
            Assert.IsTrue(notExpandedSummaryRowImpressions > notExpandedImpressions);

            // Collapsed grid call with collapsed rows expanded
            collapsedRow = new { ExpandCollapsedRows = true, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumnValue } };

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: collapsedRow);

            Assert.IsNotNull(result);

            var expandedRowCount = int.Parse(result["@odata.count"].ToString());

            var expandedImpressions = 0;
            var expandedSummaryRowImpressions = 0;
            foreach (var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    expandedImpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    expandedSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Expanded row grid summary rows must match rows from this grid. This total will not include non-zero impressions so this total will be lesser
            Assert.AreEqual(expandedImpressions, expandedSummaryRowImpressions);
            Assert.IsTrue(notExpandedSummaryRowImpressions > expandedSummaryRowImpressions);
            Assert.AreEqual(expandedImpressions + notExpandedImpressions, totalInpressions);
            Assert.AreEqual(totalRowCount, expandedRowCount + notExpandedRowCount);

        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_CollapsedRow_FilterOnCollapseColumn_Success()
        {
            if (SkipTestInSI())
            {
                return;
            }
            ReportDataDownloadTestHelper.InitializePublisherUsageReportMockData(customerInfo, false, false, false, adGroupCount: 10);
            dynamic filter = new[] { new { Filter = PrepareFilterWithOperator("Spend", "30", "LessThan") } };

            // No collapsed rows to get baseline data
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "Spend",
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: null);

            Assert.IsNotNull(result);

            var totalRowCount = int.Parse(result["@odata.count"].ToString());
            var rowsWith0Spend = 0;
            var totalInpressions = 0;
            var totalSummaryRowImpressions = 0;

            foreach (var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var spend = row["Spend"]["Value"].ToString();
                    if (spend == "0.00")
                    {
                        rowsWith0Spend++;
                    }

                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    totalInpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    totalSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Impression count matches between rows and total row
            Assert.AreEqual(totalSummaryRowImpressions, totalInpressions);

            var rowsWithNon0Spend = totalRowCount - rowsWith0Spend;
            dynamic collapsedRowConditionInvalidColumnValue = PrepareFilterWithOperator("Spend", "0", "Equals");

            // Main grid call with collapsed rows not expanded
            dynamic collapsedRow = new { ExpandCollapsedRows = false, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumnValue } };

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "Spend",
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: collapsedRow);

            Assert.IsNotNull(result);

            var notExpandedRowCount = int.Parse(result["@odata.count"].ToString());
            Assert.AreEqual(rowsWithNon0Spend, notExpandedRowCount);

            var notExpandedImpressions = 0;
            var notExpandedSummaryRowImpressions = 0;
            foreach (var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var spend = row["Spend"]["Value"].ToString();
                    if (spend == "0.00")
                    {
                        Assert.Fail("0 Spend data not expected here");
                    }

                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    notExpandedImpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    notExpandedSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Summary row in main grid should include impressions from collapsed rows as well
            Assert.AreEqual(notExpandedSummaryRowImpressions, totalSummaryRowImpressions);
            Assert.IsTrue(notExpandedSummaryRowImpressions > notExpandedImpressions);

            // Collapsed grid call with collapsed rows expanded
            collapsedRow = new { ExpandCollapsedRows = true, CollapseCondition = new { Filter = collapsedRowConditionInvalidColumnValue } };

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "PublisherUsagePerformanceReport",
                new[]
                {
                    "Spend",
                    "PublisherUrl",
                    "AdGroupId",
                    "CampaignId",
                    "Impressions",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                collapsedRow: collapsedRow);

            Assert.IsNotNull(result);

            var expandedRowCount = int.Parse(result["@odata.count"].ToString());
            Assert.AreEqual(rowsWith0Spend, expandedRowCount);

            var expandedImpressions = 0;
            var expandedSummaryRowImpressions = 0;
            foreach (var row in result["value"])
            {
                if (row["RowType"].ToString() == "Normal")
                {
                    var spend = row["Spend"]["Value"].ToString();
                    if (spend != "0.00")
                    {
                        Assert.Fail("non-zero Spend data not expected here");
                    }

                    var impressions = int.Parse(row["Impressions"]["Value"].ToString());
                    expandedImpressions += impressions;
                }
                else if (row["RowType"].ToString() == "Total")
                {
                    expandedSummaryRowImpressions = int.Parse(row["Impressions"].ToString());
                }
            }

            // Expanded row grid summary rows must match rows from this grid. This total will not include non-zero impressions so this total will be lesser
            Assert.AreEqual(expandedImpressions, expandedSummaryRowImpressions);
            Assert.IsTrue(notExpandedSummaryRowImpressions > expandedSummaryRowImpressions);
            Assert.AreEqual(expandedImpressions + notExpandedImpressions, totalInpressions);

        }


        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_InvalidFilterValue_BadRequest(string schema)
        {
            dynamic keywordIdFilter = PrepareFilterWithOperator("KeywordId", "***************************", "Equals");
            dynamic filter = new[] { new { Filter = keywordIdFilter } };

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.LastSevenDays, "KeywordPerformanceReport",
                new[]
                {
                    "KeywordId",
                    "AdGroupId",
                    "CampaignId",
                    "CampaignName",
                    "Impressions",
                    "Spend",
                    "Ctr",
                    "ConversionRate"
                }, 1033,
                null, filter, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray(),
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest, schemaName: schema);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_GoalAndFunnelReport_AllRevenueFormatting()
        {
            var expectRowCount = 0;
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportDataDownloadTestHelper.InitializeGoalsAndFunnelsReportMockData(customerInfo, true, true);
                expectRowCount = 1;
            }
            
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.Today, "GoalsAndFunnelsReport",
                new[]
                {
                    "AccountId",
                    "Keyword",
                    "KeywordId",
                    "Goal",
                    "AllConversions",
                    "Assists",
                    "AllRevenue",
                    "GoalId",
                    "GoalType",
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            if(expectRowCount == 1)
            {
                var allRevenueReturned = result["value"][0]["AllRevenue"]["Value"].ToString();
                Assert.AreEqual("1.00", allRevenueReturned);
            }
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_GoalAndFunnelReport_PMax()
        {
            var expectRowCount = 0;
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportDataDownloadTestHelper.InitializeGoalsAndFunnelsReportMockData(customerInfo, true, true, isPMax: true);
                expectRowCount = 1;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.Today, "GoalsAndFunnelsReport",
                new[]
                {
                    "AccountId",
                    "Keyword",
                    "KeywordId",
                    "CampaignId",
                    "CampaignType",
                    "AssetGroupId",
                    "AssetGroupName",
                    "AdGroupId",
                    "AdGroupName",
                    "Goal",
                    "AllConversions",
                    "Assists",
                    "AllRevenue",
                    "GoalId",
                    "GoalType",
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            if (expectRowCount == 1)
            {
                var allRevenueReturned = result["value"][0]["AllRevenue"]["Value"].ToString();
                Assert.AreEqual("1.00", allRevenueReturned);

                var adGroupId = result["value"][0]["AdGroupId"]["Value"].ToString();
                Assert.IsTrue(string.IsNullOrEmpty(adGroupId));
                var adGroupName = result["value"][0]["AdGroupName"]["Value"].ToString();
                Assert.IsTrue(string.IsNullOrEmpty(adGroupName));

                var assetGroupId = result["value"][0]["AssetGroupId"]["Value"].ToString();
                Assert.IsFalse(string.IsNullOrEmpty(assetGroupId));
                var assetGroupName = result["value"][0]["AssetGroupName"]["Value"].ToString();
                Assert.IsFalse(string.IsNullOrEmpty(assetGroupName));
                var keywordId = result["value"][0]["KeywordId"]["Value"].ToString();
                Assert.AreEqual("[0]", keywordId);
            }
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_GoalAndFunnelReport_DSA()
        {
            var expectRowCount = 0;
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportDataDownloadTestHelper.InitializeGoalsAndFunnelsReportMockData(customerInfo, true, true, isDSA: true);
                expectRowCount = 1;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, null, null, DateRangePreset.Today, "GoalsAndFunnelsReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "AdGroupId",
                    "Keyword",
                    "KeywordId",
                    "Goal",
                    "AllConversions",
                    "Assists",
                    "AllRevenue",
                    "GoalId",
                    "GoalType",
                }, 1033,
                null, null, null, customerInfo, null,
                null, customerInfo.AccountIds.ToArray());

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            if (expectRowCount == 1)
            {
                var allRevenueReturned = result["value"][0]["AllRevenue"]["Value"].ToString();
                Assert.AreEqual("1.00", allRevenueReturned);
                var keywordId = result["value"][0]["KeywordId"]["Value"].ToString();
                Assert.AreEqual("[0]", keywordId);
            }
        }

        private static readonly string[] GenreReportColums = new string[]  {
                    "AccountName",
                    "AccountId",
                    "AccountNumber",
                    "CampaignName",
                    "CampaignId",
                    "AdGroupName",
                    "AdGroupId",
                    "Genre",
                    "Impressions",
                    "Spend",
                    "AverageCPM",
                    "AverageCPV",
                    "VideoViewsAt25Percent",
                    "VideoViewsAt50Percent",
                    "VideoViewsAt75Percent",
                    "CompletedVideoViews",
                    "VideoCompletionRate"
                };

        private static Tuple<int, int> FirstPage = Tuple.Create(1, 20);
        private static Tuple<int, int> SecondPage = Tuple.Create(2, 20);

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_GenreTest_CIOnly()
        {
            if (TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                // some more changes needed before enabling on SI.
                return;
            }

            SetUpCollectionData(this.customerInfo, noOfCampaigns: 2, noOfAdGroups: 0, noOfKeywords: 0, noOfAds: 0);

            // bad request cases
            // ask for report at customer level.
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                isCustomerLevel: true, startDate: null, endDate: null, DateRangePreset.Today, "GenrePerformanceReport",
                GenreReportColums, lcid: 1033, reportSort: null, filter: null, pagination: FirstPage, customerInfo,
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest);

            // ask for report for the first account.
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                isCustomerLevel: false, null, null, DateRangePreset.Today, "GenrePerformanceReport",
                reportColumns: GenreReportColums, lcid: 1033, reportSort: null, filter: null, pagination: SecondPage, customerInfo);

            Assert.IsNotNull(result);
            // assert total count.
            Assert.AreEqual(200, result["@odata.count"].Value);
            // assert count for first page.
            Assert.AreEqual(21, result["value"].Count);
            Assert.AreEqual("Total", result["value"][20]["RowType"].ToString());

            // filter one campaign id. We won't actually look for real campaign filter but just validates that the campaign filter code path
            // does not cause any unexpected failure.
            var campaignScopes = new Tuple<long, long>[2];
            campaignScopes[0] = Tuple.Create((long)customerInfo.AccountIds[0], this.campaignCollection.Campaigns[0].Data.Id);
            campaignScopes[1] = Tuple.Create((long)customerInfo.AccountIds[0], this.campaignCollection.Campaigns[1].Data.Id);

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                isCustomerLevel: false, null, null, DateRangePreset.Today, "GenrePerformanceReport",
                reportColumns: GenreReportColums, lcid: 1033, reportSort: null, filter: null, pagination: FirstPage, customerInfo, campaignScopes: campaignScopes);

            Assert.IsNotNull(result);
            // assert total count.
            Assert.AreEqual(200, result["@odata.count"].Value);
            // assert count for first page.
            Assert.AreEqual(21, result["value"].Count);
            Assert.AreEqual("Total", result["value"][20]["RowType"].ToString());
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreviewInlineChart()
        {
            var expectRowCount = 7;
            var yesterday = DateTime.Now.AddDays(-1).Date;
            var expectedCtr = "0";
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.customerInfo, 1, 2, 2);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.customerInfo, yesterday, DateTime.Now, populateAccount: true);
                expectedCtr = "180";
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.ThisWeekFromSun, "KeywordPerformanceReport",
                new[]
                {
                    "GregorianDate",
                    "Ctr",
                    "Spend"
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId }, null, null, null, isInlineChart: true
            );

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);
            Assert.AreEqual("Ctr", result["value"][0]["MetricName"].ToString());
            Assert.AreEqual("Spend", result["value"][1]["MetricName"].ToString());
            for (int i = 0; i < expectRowCount; i++)
            {
                var date = DateTime.Parse(result["value"][0]["PrimaryDataPoints"][i]["DateTime"].ToString());
                var value = result["value"][0]["PrimaryDataPoints"][i]["Value"].ToString();
                if (yesterday.Equals(date))
                {
                    Assert.AreEqual(expectedCtr, value);
                }
            }
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_MultiAccount_AccountShard(string schema)
        {
            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                SetUpCollectionData(this.multiAccountCustomer, 1, 1, 2);
                ReportPreviewTestHelper.InitializeKeywordMockPerfData(
                    this.adGroupCollection, this.keywordCollection, this.multiAccountCustomer, DateTime.Now.AddDays(-1), DateTime.Now, populateCampaign: true);
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.AllTime, "CampaignPerformanceReport",
                new[]
                {
                    "AccountId",
                    "AccountName",
                    "CampaignId",
                    "CampaignName",
                    "Clicks",
                    "CTR",
                    "AverageCPC",
                    "Spend"
                }, 1033, null, null, null, multiAccountCustomer, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { multiAccountCustomer.CustomerId }, null, null, null, schemaName: schema
            );
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [Ignore]
        public void ReportPreview_ReachColumns_AdGroupReport()
        {
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.Yesterday, "AdGroupPerformanceReport",
                new[]
                {
                    "AdGroupId",
                    "AdGroupName",
                    "Clicks",
                    "Impressions",
                    "Reach",
                    "AverageImpressionFrequencyPerUser",
                    "AverageImpressionFrequencyPerUser7Day"
                }, 1033, null, null, null, customerInfo, 
                customerScopes: new long[] { customerInfo.CustomerId }, 
                expectedHttpStatusCode: System.Net.HttpStatusCode.InternalServerError);

            Assert.IsNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [Ignore]
        public void ReportPreview_ReachColumns_AdReport()
        {
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.Yesterday, "AdPerformanceReport",
                new[]
                {
                    "AdId",
                    "AdTitle",
                    "Clicks",
                    "Impressions",
                    "Reach",
                    "AverageImpressionFrequencyPerUser",
                    "AverageImpressionFrequencyPerUser7Day"
                }, 1033, null, null, null, customerInfo,
                customerScopes: new long[] { customerInfo.CustomerId },
                expectedHttpStatusCode: System.Net.HttpStatusCode.InternalServerError);

            Assert.IsNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        [Ignore]
        public void ReportPreview_ReachColumns_CampaignReport()
        {
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.Yesterday, "CampaignPerformanceReport",
                new[]
                {
                    "CampaignId",
                    "CampaignName",
                    "Clicks",
                    "Impressions",
                    "Reach",
                    "AverageImpressionFrequencyPerUser",
                    "AverageImpressionFrequencyPerUser7Day"
                }, 1033, null, null, null, customerInfo,
                customerScopes: new long[] { customerInfo.CustomerId },
                expectedHttpStatusCode: System.Net.HttpStatusCode.InternalServerError);

            Assert.IsNull(result);
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_CustomTimeRangeValidations(string schema)
        {
            var startDate = DateTime.UtcNow.Date.AddYears(-4);
            var endDate = startDate.AddDays(1);
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                    "TopImpressionRatePercent",
                    "ImpressionSharePercent",
                    "AbsoluteTopImpressionSharePercent",
                }, 1033, null, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId }, expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest, schemaName: schema);

            Assert.IsNotNull(result);
            Assert.AreEqual("InvalidDateRange", result["value"][0]["Code"].ToString());

            startDate = DateTime.UtcNow.Date.AddYears(-1);
            endDate = startDate.AddDays(-1);
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                true, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                    "TopImpressionRatePercent",
                    "ImpressionSharePercent",
                    "AbsoluteTopImpressionSharePercent",
                }, 1033, null, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId }, expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest, schemaName: schema);

            Assert.IsNotNull(result);
            Assert.AreEqual("InvalidDateRange", result["value"][0]["Code"].ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_AccountPerformanceReport_AppCampaignMetrics(string schema)
        {
            if (CustomerInfo.DefaultCustomerPilot.HasValue && CustomerInfo.DefaultCustomerPilot.Value == ClickhouseMigrationConstants.DatamartClickhouseMigrationPhase2Feature)
            {
                //Ignore for clickhouse pipeline.
                return;
            }
            var expectRowCount = 0;
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date;

            if (!TestEnvConfiguration.ENVName_SI.Equals(TestSetting.Environment.EnvironmentName))
            {
                ReportPreviewTestHelper.InitializeAccountReportMockPerfData(customerInfo, startDate, endDate, haveAppCampaignMetrics: true);
                expectRowCount = 2;
            }

            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AccountPerformanceReport",
                new[]
                {
                    "AccountNumber",
                    "AccountName",
                    "AdDistribution",
                    "Impressions",
                    "Downloads",
                    "PostClickDownloadRate",
                    "CostPerDownload",
                    "AppInstalls",
                    "PostClickInstallRate",
                    "CPI",
                    "Purchases",
                    "PostInstallPurchaseRate",
                    "CPP",
                    "Subscriptions",
                    "PostInstallSubscriptionRate",
                    "CPS",
                }, 1033, null, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId }, schemaName: schema);

            Assert.IsNotNull(result);
            Assert.AreEqual(expectRowCount, result["@odata.count"].Value);

            for (int i = 0; i < expectRowCount; i++)
            {
                Assert.AreEqual("2", result["value"][0]["Downloads"]["Value"].ToString());
            }
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_AppsPerformanceReport()
        {
            if (CustomerInfo.DefaultCustomerPilot.GetValueOrDefault(0) == Features.DatamartClickhouseMigrationPhase2)
            {
                Console.WriteLine($"Skipping tests because DefaultCustomerPilot is {Features.DatamartClickhouseMigrationPhase2}");
				return;
            }

            var (campaignCollection, assetGroupCollection) = ReportDataDownloadTestHelper.SetUpPerformanceMaxCollectionData(customerInfo, 1, 1);

            var assetGroupData = new BiData
            {
                Date = DateTime.UtcNow.Date,
                CustomerId = customerInfo.CustomerId,
                AccountId = customerInfo.AccountIds[0],
                CampaignId = campaignCollection.Ids[0],
                AssetGroupId = assetGroupCollection[0].AssetGroupsIds[0],
                Impressions = 120,
                Clicks = 60,
                Spent = 20,
                Downloads = 40,
                FirstLaunches = 30,
                Purchases = 10,
                Subscriptions = 5,
                AdvertiserReportedRevenue = 100,
            };
            BiDatabaseHelper.SetAssetGroupMockBiData(customerInfo, assetGroupData.SingleAsEnumerable(), false);


            var command = new Data.SqlClient.SqlCommand($"UPDATE dbo.Campaign SET AdvertisingChannelTypeId = 3, CampaignFeatureBitMask = 128 WHERE CampaignId = {assetGroupData.CampaignId} AND AccountId = {assetGroupData.AccountId}");
            DatabaseHelper.ExecuteCommandOnPartitionTolerantToTimeout(
                TestSetting.Environment.CampaignService.AdvbiAzureAdgroupDB,
                AdvbiDatabaseShardHelper.GetAdGroupPartitionId(assetGroupData.AssetGroupId.Value),
                command);
            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date;
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AppsPerformanceReport",
                new[]
                {
                    "Year",
                    "AccountId",
                    "CampaignId",
                    "CampaignName",
                    "AssetGroupId",
                    "AssetGroupName",
                    "Impressions",
                    "Clicks",
                    "Downloads",
                    "PostClickDownloadRate",
                    "CostPerDownload",
                    "AppInstalls",
                    "PostClickInstallRate",
                    "CPI",
                    "Purchases",
                    "PostInstallPurchaseRate",
                    "CPP",
                    "Subscriptions",
                    "PostInstallSubscriptionRate",
                    "CPS",
                    "Revenue",
                    "ReturnOnAdSpend",
                    "RevenuePerDownload",
                    "RevenuePerAppInstall"
            }, 1033, null, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
            Assert.AreEqual("1", result["@odata.count"].ToString());

            var row = result["value"][0];
            Assert.AreEqual($"[{assetGroupData.AccountId}]", row["AccountId"]["Value"].ToString());


            Assert.AreEqual("40", row["Downloads"]["Value"].ToString());
            Assert.AreEqual("66.67%", row["PostClickDownloadRate"]["Value"].ToString());
            Assert.AreEqual("0.50", row["CostPerDownload"]["Value"].ToString());
            Assert.AreEqual("30", row["AppInstalls"]["Value"].ToString());
            Assert.AreEqual("50.00%", row["PostClickInstallRate"]["Value"].ToString());
            Assert.AreEqual("0.67", row["CPI"]["Value"].ToString());
            Assert.AreEqual("10", row["Purchases"]["Value"].ToString());
            Assert.AreEqual("33.33%", row["PostInstallPurchaseRate"]["Value"].ToString());
            Assert.AreEqual("2.00", row["CPP"]["Value"].ToString());
            Assert.AreEqual("5", row["Subscriptions"]["Value"].ToString());
            Assert.AreEqual("16.67%", row["PostInstallSubscriptionRate"]["Value"].ToString());
            Assert.AreEqual("4.00", row["CPS"]["Value"].ToString());
            Assert.AreEqual("100.00", row["Revenue"]["Value"].ToString());
            Assert.AreEqual("500.00%", row["ReturnOnAdSpend"]["Value"].ToString());
            Assert.AreEqual("2.50", row["RevenuePerDownload"]["Value"].ToString());
            Assert.AreEqual("3.33", row["RevenuePerAppInstall"]["Value"].ToString());

        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_SearchInsightReport_CIOnly()
        {
            //TODO: Add more test cases.
            DateTime now = DateTime.Now;
            SetUpCollectionData(this.customerInfo, this.numOfCampaigns, this.numOfAdGroups, this.numOfKeywordsPerAdGroup);
            ReportDataDownloadTestHelper.InitializeSearchInsightMockPerfData(this.adGroupCollection, this.keywordCollection, this.customerInfo, now, now, useRandomSearchTerm: true, isPerfMax: true);

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date;

            // bad request cases
            // ask for report at customer level.
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                isCustomerLevel: true, startDate: null, endDate: null, DateRangePreset.Today, "SearchInsightPerformanceReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "SearchCategory",
                    "SearchQuery",
                    "SearchVolume",
                    "Impressions",
                    "Clicks",
                    "Spend",
                    "Conversions"
                }, lcid: 1033, reportSort: null, filter: null, pagination: FirstPage, customerInfo,
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest);
            // ask for report for multiple accounts.
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                isCustomerLevel: false, startDate: null, endDate: null, DateRangePreset.Today, "SearchInsightPerformanceReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "SearchCategory",
                    "SearchQuery",
                    "SearchVolume",
                    "Impressions",
                    "Clicks",
                    "Spend",
                    "Conversions"
                }, lcid: 1033, reportSort: null, filter: null, pagination: FirstPage, customerInfo, accountIds: new int[] { multiAccountCustomer.AccountIds[0], multiAccountCustomer.AccountIds[1] },
                expectedHttpStatusCode: System.Net.HttpStatusCode.BadRequest);

            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "SearchInsightPerformanceReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "SearchCategory",
                    "SearchQuery",
                    "SearchVolume",
                    "Impressions",
                    "Clicks",
                    "Spend",
                    "Conversions"
                }, 1033, null, null, null, customerInfo,customerScopes: new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_InsertionOrderReport_CIOnly()
        {
            // TODO: Try to add more tests later after the full logic in sproc is done.
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, null, null, DateRangePreset.LastThirtyDays, "InsertionOrderReport",
                new[]
                {
                    "Date",
                    "AccountName",
                    "AccountNumber",
                    "ChangeType",
                    "ChangeAmount",
                    "InsertionOrderDetails"
                }, 1033, null, null, null, customerInfo, TimeZone.GreenwichMeanTimeDublinEdinburghLisbonLondon,
                new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
        }


        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_AssetPerformanceReport()
        {
            if (CustomerInfo.DefaultCustomerPilot.GetValueOrDefault(0) == Features.DatamartClickhouseMigrationPhase2)
            {
                Console.WriteLine($"Skipping tests because DefaultCustomerPilot is {Features.DatamartClickhouseMigrationPhase2}");
				return;
            }

            SetUpCollectionData(this.customerInfo, this.numOfCampaigns, this.numOfAdGroups, this.numOfKeywordsPerAdGroup);
            var assetData = new BiData
            {
                Date = DateTime.UtcNow.Date,
                CustomerId = customerInfo.CustomerId,
                AccountId = customerInfo.AccountIds[0],
                CampaignId = campaignCollection.Ids[0],
                OrderId = adGroupCollection[0].AdGroupsIds[0],
                AdGroupName = "My adgroup name",
                AssetId = 1234,
                AdAssetAssociationTypeId = 11,
                Clicks = 60,
                Spent = 20,
                Conversions = 9, 
                AdvertiserReportedRevenue = 10.2,
                AssetBIFields = new AssetBIFields
                {
                    AssetText = "My cool asset",
                    PerformanceLabelBestCount = 120,
                    PerformanceLabelGoodCount = 0,
                    PerformanceLabelLearningCount = 0,
                    PerformanceLabelLowCount = 0,
                    PerformanceLabelUnratedCount = 0,
                    IsAdvertiserProvided = true

                }
            };
            var videoAssetData = new BiData
            {
                Date = DateTime.UtcNow.Date,
                CustomerId = customerInfo.CustomerId,
                AccountId = customerInfo.AccountIds[0],
                CampaignId = campaignCollection.Ids[0],
                OrderId = adGroupCollection[0].AdGroupsIds[0],
                AdGroupName = "My adgroup name",
                AssetId = 1235,
                AdAssetAssociationTypeId = 21,
                Clicks = 60,
                Spent = 20,
                VideoViews = 20,
                VideoViewsAt25Percent = 10,
                VideoViewsAt50Percent = 5,
                VideoViewsAt75Percent = 3,
                CompletedVideoViews = 2,
                Conversions = 10, 
                AdvertiserReportedRevenue = 10.3,
                AssetBIFields = new AssetBIFields
                {
                    AssetText = "{\"720\":\"https://se1.mavideo.microsoft.com/mockvideo.mp4\"}",
                    PerformanceLabelBestCount = 120,
                    IsAdvertiserProvided = true

                }
            };

            BiDatabaseHelper.SetAssetAdGroupSnapShotMockBiData(customerInfo, new List<BiData> { assetData, videoAssetData}, false);

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date;
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AssetPerformanceReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "AdGroupId",
                    "AdGroupName",
                    "AssetId",
                    "AssetContent",
                    "AssetType",
                    "AssetSource",
                    "Impressions",
                    "Clicks",
                    "VideoViews",
                    "VideoViewsAt25Percent",
                    "VideoViewsAt50Percent",
                    "VideoViewsAt75Percent",
                    "CompletedVideoViews",
                    "VideoCompletionRate",
                    "Conversions",
                    "Revenue"
            }, 1033, null, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
            Assert.AreEqual("2", result["@odata.count"].ToString());
            Assert.IsNotNull(result["value"], "Result value should not be null.");
            foreach (var data in result["value"])
            {
                Assert.IsNotNull(data["RowType"]);
                if(data["RowType"].ToString() == "Normal")
                {
                    Assert.IsNotNull(data["AssetType"]);
                    Assert.IsNotNull(data["AssetType"]["Value"], "AssetType value should not be null.");

                    if (data["AssetType"]["Value"].ToString() == "Headline")
                    {
                        Assert.AreEqual("Headline", data["AssetType"]["Value"].ToString());
                        Assert.AreEqual($"[{assetData.OrderId}]", data["AdGroupId"]["Value"].ToString());
                        Assert.AreEqual(assetData.AdGroupName, data["AdGroupName"]["Value"].ToString());
                        Assert.AreEqual("My cool asset", data["AssetContent"]["Value"].ToString());
                        Assert.AreEqual("[1234]", data["AssetId"]["Value"].ToString());
                        Assert.AreEqual("Advertiser Provided", data["AssetSource"]["Value"].ToString());
                        Assert.AreEqual("120", data["Impressions"]["Value"].ToString());
                        Assert.AreEqual("60", data["Clicks"]["Value"].ToString());
                        Assert.AreEqual("0", data["VideoViews"]["Value"].ToString());
                        Assert.AreEqual("0", data["VideoViewsAt25Percent"]["Value"].ToString());
                        Assert.AreEqual("0", data["VideoViewsAt50Percent"]["Value"].ToString());
                        Assert.AreEqual("0", data["VideoViewsAt75Percent"]["Value"].ToString());
                        Assert.AreEqual("0.00%", data["VideoCompletionRate"]["Value"].ToString());
                        Assert.AreEqual("9", data["Conversions"]["Value"].ToString());
                        Assert.AreEqual("10.20", data["Revenue"]["Value"].ToString());
                    }
                    else if (data["AssetType"]["Value"].ToString() == "Video")
                    {
                        Assert.AreEqual("Video", data["AssetType"]["Value"].ToString());
                        Assert.AreEqual($"[{videoAssetData.OrderId}]", data["AdGroupId"]["Value"].ToString());
                        Assert.AreEqual(videoAssetData.AdGroupName, data["AdGroupName"]["Value"].ToString());
                        Assert.AreEqual("{\"720\":\"https://se1.mavideo.microsoft.com/mockvideo.mp4\"}", data["AssetContent"]["Value"].ToString());
                        Assert.AreEqual("[1235]", data["AssetId"]["Value"].ToString());
                        Assert.AreEqual("Advertiser Provided", data["AssetSource"]["Value"].ToString());
                        Assert.AreEqual("120", data["Impressions"]["Value"].ToString());
                        Assert.AreEqual("60", data["Clicks"]["Value"].ToString());
                        Assert.AreEqual("20", data["VideoViews"]["Value"].ToString());
                        Assert.AreEqual("10", data["VideoViewsAt25Percent"]["Value"].ToString());
                        Assert.AreEqual("5", data["VideoViewsAt50Percent"]["Value"].ToString());
                        Assert.AreEqual("3", data["VideoViewsAt75Percent"]["Value"].ToString());
                        Assert.AreEqual("2", data["CompletedVideoViews"]["Value"].ToString());
                        Assert.AreEqual("1.67%", data["VideoCompletionRate"]["Value"].ToString());
                        Assert.AreEqual("10", data["Conversions"]["Value"].ToString());
                        Assert.AreEqual("10.30", data["Revenue"]["Value"].ToString());
                    }
                }else if(data["RowType"].ToString() == "Total")
                {
                    Assert.AreEqual("240", data["Impressions"].ToString());
                    Assert.AreEqual("120", data["Clicks"].ToString());
                    Assert.AreEqual("20", data["VideoViews"].ToString());
                    Assert.AreEqual("10", data["VideoViewsAt25Percent"].ToString());
                    Assert.AreEqual("5", data["VideoViewsAt50Percent"].ToString());
                    Assert.AreEqual("3", data["VideoViewsAt75Percent"].ToString());
                    Assert.AreEqual("0.83%", data["VideoCompletionRate"].ToString());
                    Assert.AreEqual("19", data["Conversions"].ToString());
                }
            }

            // Test sorting
            var reportSort = new Tuple<string, string>[] { Tuple.Create("VideoViews", "DESCENDING"), };
            var sortResult = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AssetPerformanceReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "AdGroupId",
                    "AdGroupName",
                    "AssetId",
                    "AssetContent",
                    "AssetType",
                    "AssetSource",
                    "Impressions",
                    "Clicks",
                    "VideoViews",
                    "VideoViewsAt25Percent",
                    "VideoViewsAt50Percent",
                    "VideoViewsAt75Percent",
                    "CompletedVideoViews",
                    "VideoCompletionRate",
                    "Conversions",
                    "Revenue"
            }, 1033, reportSort, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId });
            Assert.IsNotNull(sortResult);
            Assert.AreEqual("2", sortResult["@odata.count"].ToString());
            var row = sortResult["value"][0];
            Assert.AreEqual("20", row["VideoViews"]["Value"].ToString());
            Assert.AreEqual("Video", row["AssetType"]["Value"].ToString());

            //Test conversion sorting
            // Test sorting
            var reportSortConv = new Tuple<string, string>[] { Tuple.Create("Conversions", "DESCENDING"), };
            var sortResultConv = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AssetPerformanceReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "AdGroupId",
                    "AdGroupName",
                    "AssetId",
                    "AssetContent",
                    "AssetType",
                    "AssetSource",
                    "Impressions",
                    "Clicks",
                    "VideoViews",
                    "VideoViewsAt25Percent",
                    "VideoViewsAt50Percent",
                    "VideoViewsAt75Percent",
                    "CompletedVideoViews",
                    "VideoCompletionRate",
                    "Conversions",
                    "Revenue"
            }, 1033, reportSortConv, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId });
            Assert.IsNotNull(sortResultConv);
            Assert.AreEqual("2", sortResultConv["@odata.count"].ToString());
            var rowConv = sortResultConv["value"][0];
            Assert.AreEqual("10", row["Conversions"]["Value"].ToString());

            // Test filtering
            dynamic assetTypeFilter = PrepareFilterWithOperator("AssetType", "4", "Equals");
            dynamic filter = new[] { new { Filter = assetTypeFilter } };
            var filterResult = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AssetPerformanceReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "AdGroupId",
                    "AdGroupName",
                    "AssetId",
                    "AssetContent",
                    "AssetType",
                    "AssetSource",
                    "Impressions",
                    "Clicks",
                    "VideoViews",
                    "VideoViewsAt25Percent",
                    "VideoViewsAt50Percent",
                    "VideoViewsAt75Percent",
                    "CompletedVideoViews",
                    "VideoCompletionRate",
                    "Conversions",
                    "Revenue"
            }, 1033, null, filter, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId });
            Assert.IsNotNull(filterResult);
            Assert.AreEqual("1", filterResult["@odata.count"].ToString());
            row = filterResult["value"][0];
            Assert.AreEqual("20", row["VideoViews"]["Value"].ToString());
            Assert.AreEqual("Video", row["AssetType"]["Value"].ToString());
        }

        [TestMethod, Priority(2)]
        [Owner(TestOwners.ReportPreview)]
        public void ReportPreview_AssetPerformanceReport_AdGroupLevel_IgnorePMaxAssets()
        {
            if (CustomerInfo.DefaultCustomerPilot.GetValueOrDefault(0) == Features.DatamartClickhouseMigrationPhase2)
            {
                Console.WriteLine($"Skipping tests because DefaultCustomerPilot is {Features.DatamartClickhouseMigrationPhase2}");
				return;
            }

            SetUpCollectionData(this.customerInfo, this.numOfCampaigns, this.numOfAdGroups, this.numOfKeywordsPerAdGroup);
            var assetData = new BiData
            {
                Date = DateTime.UtcNow.Date,
                CustomerId = customerInfo.CustomerId,
                AccountId = customerInfo.AccountIds[0],
                CampaignId = campaignCollection.Ids[0],
                OrderId = adGroupCollection[0].AdGroupsIds[0],
                AdGroupName = "My adgroup name",
                AssetId = 1234,
                AdAssetAssociationTypeId = 11,
                Clicks = 10,
                AssetBIFields = new AssetBIFields
                {
                    AssetText = "My cool asset",
                    PerformanceLabelBestCount = 20,
                    PerformanceLabelGoodCount = 0,
                    PerformanceLabelLearningCount = 0,
                    PerformanceLabelLowCount = 0,
                    PerformanceLabelUnratedCount = 0,
                    IsAdvertiserProvided = true
                }
            };

            // we simulate a pmax asset by not specifying AdGroupName
            // this should mean that the asset data is not returned when we request
            // AdGroup-related fields
            var pmaxAssetData = new BiData
            {
                Date = DateTime.UtcNow.Date,
                CustomerId = customerInfo.CustomerId,
                AccountId = customerInfo.AccountIds[0],
                CampaignId = campaignCollection.Ids[1],
                OrderId = adGroupCollection[1].AdGroupsIds[0],
                AssetId = 5678,
                AdAssetAssociationTypeId = 11,
                Clicks = 10,
                AssetBIFields = new AssetBIFields
                {
                    AssetText = "My cool asset",
                    PerformanceLabelBestCount = 20,
                    PerformanceLabelGoodCount = 0,
                    PerformanceLabelLearningCount = 0,
                    PerformanceLabelLowCount = 0,
                    PerformanceLabelUnratedCount = 0,
                    IsAdvertiserProvided = true
                }
            };

            BiDatabaseHelper.SetAssetAdGroupSnapShotMockBiData(customerInfo, new List<BiData> { assetData, pmaxAssetData }, false);

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date;
            // Include AdGroupId, pmax asset should be excluded
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AssetPerformanceReport",
                new[]
                {
                    "AccountId",
                    "CampaignId",
                    "AdGroupId",
                    "Impressions",
                    "Clicks"
            }, 1033, null, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
            Assert.AreEqual("1", result["@odata.count"].ToString());

            var row = result["value"][0];
            Assert.AreEqual($"[{assetData.OrderId}]", row["AdGroupId"]["Value"].ToString());

            Assert.AreEqual("20", row["Impressions"]["Value"].ToString());
            Assert.AreEqual("10", row["Clicks"]["Value"].ToString());

            // Exclude AdGroupId, pmax asset should be included
            result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AssetPerformanceReport",
                new[]
                {
                    "AccountId",
                    "Impressions",
                    "Clicks"
            }, 1033, null, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
            Assert.AreEqual("1", result["@odata.count"].ToString());

            row = result["value"][0];

            Assert.AreEqual("40", row["Impressions"]["Value"].ToString());
            Assert.AreEqual("20", row["Clicks"]["Value"].ToString());

        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [Priority(2)]
        public void ReportPreview_CombinationReport_CIOnly()
        {
            DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, new int[] { Features.CombinationReports });
            //DatabaseHelper.EnablePilotFeatures(this.customerInfo.CustomerId, false, new int[] { Features.AssetReports });
            //TODO: Add more test cases.
            DateTime now = DateTime.Now;
            SetUpCollectionData(this.customerInfo, this.numOfCampaigns, this.numOfAdGroups, this.numOfKeywordsPerAdGroup);
            ReportDataDownloadTestHelper.InitializeCombinationMockPerfData(this.customerInfo, this.adGroupCollection, this.keywordCollection, now, now, isPerfMax: true, populateAccount: true, populateCampaign: true);

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date;

            var reportSort = new Tuple<string, string>[] { Tuple.Create("CombinationLongHeadline", "DESCENDING"), };
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "CombinationPerformanceReport",
                new[]
                {
                    "AccountName",
                    "AccountId",
                    "CampaignName",
                    "CampaignId",
                    "CampaignType",
                    "AdGroupId",
                    "AdGroupName",
                    "AssetGroupId",
                    "AssetGroupName",
                    "AdId",
                    "AdType",
                    "Headline1",
                    "Headline2",
                    "Headline3",
                    "CombinationLongHeadline",
                    "Description1",
                    "Description2",
                    "Image",
                    "Logo",
                    "Impressions",
                    "Clicks",
                    "Spend",
                    "CTR"
                }, 1033, reportSort, null, null, customerInfo, customerScopes: new long[] { customerInfo.CustomerId });

            Assert.IsNotNull(result);
        }

        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_AssetGroupPerformanceReport_PMax(string schema)
        {

            var (campaignCollection, assetGroupCollection) = ReportDataDownloadTestHelper.SetUpPerformanceMaxCollectionData(customerInfo, 1, 1);

            var assetGroupData = new BiData
            {
                Date = DateTime.UtcNow.Date,
                CustomerId = customerInfo.CustomerId,
                AccountId = customerInfo.AccountIds[0],
                CampaignId = campaignCollection.Ids[0],
                CampaignName = campaignCollection.Campaigns[0].Data.Name,
                AdvertisingChannelTypeId = 9,
                AssetGroupId = assetGroupCollection[0].AssetGroupsIds[0],
                Impressions = 120,
                Clicks = 60,
                Spent = 20,
                Conversions = 10,
                AdvertiserReportedRevenue = 100,
            };
            BiDatabaseHelper.SetAssetGroupMockBiData(customerInfo, assetGroupData.SingleAsEnumerable(), false);
            BiDatabaseHelper.SetCampaignMockBiData(customerInfo, assetGroupData.SingleAsEnumerable(), false, false);

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date;
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AssetGroupPerformanceReport",
                [
                    "GregorianDate",
                    "AccountId",
                    "CampaignId",
                    "CampaignName",
                    "CampaignType",
                    "AssetGroupId",
                    "AssetGroupName",
                    "Impressions",
                    "Clicks",
                    "Ctr",
                    "AverageCpc",
                    "Conversions",
                    "Revenue"
                ], 1033, null, null, null, customerInfo, customerScopes: [customerInfo.CustomerId], schemaName: schema);

            Assert.IsNotNull(result);
            Assert.AreEqual("1", result["@odata.count"].ToString());

            var row = result["value"][0];
            Assert.AreEqual($"[{assetGroupData.AccountId}]", row["AccountId"]["Value"].ToString());
            Assert.AreEqual($"[{assetGroupData.CampaignId}]", row["CampaignId"]["Value"].ToString());
            Assert.AreEqual(campaignCollection.Campaigns[0].Data.Name, row["CampaignName"]["Value"].ToString());
            Assert.AreEqual("Performance max", row["CampaignType"]["Value"].ToString());
            Assert.AreEqual($"[{assetGroupData.AssetGroupId}]", row["AssetGroupId"]["Value"].ToString());

            Assert.AreEqual("120", row["Impressions"]["Value"].ToString());
            Assert.AreEqual("60", row["Clicks"]["Value"].ToString());
            Assert.AreEqual("50.00%", row["Ctr"]["Value"].ToString());
            Assert.AreEqual("0.33", row["AverageCpc"]["Value"].ToString());
            Assert.AreEqual("10", row["Conversions"]["Value"].ToString());
            Assert.AreEqual("100.00", row["Revenue"]["Value"].ToString());
        }


        [TestMethod]
        [Owner(TestOwners.ReportPreview)]
        [DataRow("UIPreviewV1")]
        [DataRow("UIUnifiedPreview")]
        public void ReportPreview_AssetGroupPerformanceReport_WindowsApp(string schema)
        {

            var (campaignCollection, assetGroupCollection) = ReportDataDownloadTestHelper.SetUpPerformanceMaxCollectionData(customerInfo, 1, 1);

            var assetGroupData = new BiData
            {
                Date = DateTime.UtcNow.Date,
                CustomerId = customerInfo.CustomerId,
                AccountId = customerInfo.AccountIds[0],
                CampaignId = campaignCollection.Ids[0],
                CampaignName = campaignCollection.Campaigns[0].Data.Name,
                AdvertisingChannelTypeId = 3,
                CampaignFeatureBitMask = 128,
                AssetGroupId = assetGroupCollection[0].AssetGroupsIds[0],
                Impressions = 120,
                Clicks = 60,
                Spent = 20,
                Conversions = 10,
                AdvertiserReportedRevenue = 100,
            };
            BiDatabaseHelper.SetAssetGroupMockBiData(customerInfo, assetGroupData.SingleAsEnumerable(), false);
            BiDatabaseHelper.SetCampaignMockBiData(customerInfo, assetGroupData.SingleAsEnumerable(), false, false);

            var startDate = DateTime.UtcNow.Date.AddDays(-3);
            var endDate = DateTime.UtcNow.Date;
            var result = ReportPreviewTestHelper.ExecuteReportPreview(
                false, startDate, endDate, null, "AssetGroupPerformanceReport",
                [
                    "GregorianDate",
                    "AccountId",
                    "CampaignId",
                    "CampaignName",
                    "CampaignType",
                    "AssetGroupId",
                    "AssetGroupName",
                    "Impressions",
                    "Clicks",
                    "Ctr",
                    "AverageCpc",
                    "Conversions",
                    "Revenue"
                ], 1033, null, null, null, customerInfo, customerScopes: [customerInfo.CustomerId], schemaName: schema);

            Assert.IsNotNull(result);
            Assert.AreEqual("1", result["@odata.count"].ToString());

            var row = result["value"][0];
            Assert.AreEqual($"[{assetGroupData.AccountId}]", row["AccountId"]["Value"].ToString());
            Assert.AreEqual($"[{assetGroupData.CampaignId}]", row["CampaignId"]["Value"].ToString());
            Assert.AreEqual(campaignCollection.Campaigns[0].Data.Name, row["CampaignName"]["Value"].ToString());
            Assert.AreEqual("App", row["CampaignType"]["Value"].ToString());
            Assert.AreEqual($"[{assetGroupData.AssetGroupId}]", row["AssetGroupId"]["Value"].ToString());

            Assert.AreEqual("120", row["Impressions"]["Value"].ToString());
            Assert.AreEqual("60", row["Clicks"]["Value"].ToString());
            Assert.AreEqual("50.00%", row["Ctr"]["Value"].ToString());
            Assert.AreEqual("0.33", row["AverageCpc"]["Value"].ToString());
            Assert.AreEqual("10", row["Conversions"]["Value"].ToString());
            Assert.AreEqual("100.00", row["Revenue"]["Value"].ToString());
        }

        private void SetUpCollectionData(CustomerInfo customerInfo, int noOfCampaigns, int noOfAdGroups, int noOfKeywords)
        {
            var (CampaignCollection, AdGroupCollection, KeywordCollection) = ReportDataDownloadTestHelper.SetUpCollectionData(customerInfo, noOfCampaigns, noOfAdGroups, noOfKeywords);
            this.campaignCollection = CampaignCollection;
            this.adGroupCollection = AdGroupCollection;
            this.keywordCollection = KeywordCollection;
        }

        private void SetUpCollectionData(CustomerInfo customerInfo, int noOfCampaigns, int noOfAdGroups, int noOfKeywords, int noOfAds)
        {
            var (CampaignCollection, AdGroupCollection, KeywordCollection, AdCollection) = ReportDataDownloadTestHelper.SetUpCollectionData(customerInfo, noOfCampaigns, noOfAdGroups, noOfKeywords, noOfAds);
            this.campaignCollection = CampaignCollection;
            this.adGroupCollection = AdGroupCollection;
            this.keywordCollection = KeywordCollection;
            this.adCollection = AdCollection;
        }

        private object PrepareFilterWithOperator(string propertyName, string filterValue1, string Operator1)
        {
            return new { LogicalOperator = "OR", Expressions = new[] { new { odatatype = "#Model.Predicate", PropertyName = propertyName, Operator = Operator1, Values = new[] { filterValue1 } } } };
        }

        private void AssertDimensionRowColumn(dynamic result, int index, string columnName, object expectedValue)
        {
            Assert.AreEqual(result["value"][index][columnName]["Value"].ToString(), expectedValue.ToString());
        }

        private void AssertBIRowColumn(dynamic result, int index, string columnName, object expectedValue)
        {
            Assert.AreEqual(expectedValue.ToString(), result["value"][index][columnName]["Value"].ToString(), $"{columnName} value");
            Assert.AreEqual(expectedValue.ToString(), result["value"][index][columnName]["RawValue"].ToString(), $"{columnName} raw value");
        }

        private void AssertSummaryRow(dynamic result, int expectRowCount, (string, string)[] expectedKeyValuePairs, string[] unexpectedColumns = null)
        {
            JArray rows = JArray.FromObject(result["value"]);
            if (expectRowCount > 0)
            {
                Assert.AreEqual(expectRowCount + 1, rows.Count);
                var summaryRow = rows[expectRowCount];
                foreach (var keyValuePair in expectedKeyValuePairs)
                {
                    Assert.AreEqual(keyValuePair.Item2, summaryRow[keyValuePair.Item1].ToString(), "summary row");
                }

                foreach (var unexpectedColumn in unexpectedColumns.OrEmpty())
                {
                    Assert.IsNull(summaryRow[unexpectedColumn], $"{unexpectedColumn} should not be included in summary row");
                }
            }
            else
            {
                Assert.AreEqual(expectRowCount, rows.Count);
            }
        }
    }
}